/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"context"
	"fmt"
)

// IStorageService interface for storage operations
type IStorageService interface {
	Get(key string, scope StorageScope) (string, error)
	Set(key string, value string, scope StorageScope, target StorageTarget) error
	Keys(scope StorageScope, target StorageTarget) []string
	HasScope(profile IUserDataProfile) bool
	StoreAll(items []StorageItem, flush bool) error
}

// ILogService interface for logging
type ILogService interface {
	Trace(message string, args ...interface{})
	Debug(message string, args ...interface{})
	Info(message string, args ...interface{})
	Warn(message string, args ...interface{})
	Error(message string, args ...interface{})
}

// IUserDataProfilesService interface for user data profiles
type IUserDataProfilesService interface {
	GetProfiles() []IUserDataProfile
	GetDefaultProfile() IUserDataProfile
	CreateProfile(name string) (IUserDataProfile, error)
	RemoveProfile(profile IUserDataProfile) error
}

// IMainProcessService interface for main process communication
type IMainProcessService interface {
	GetChannel(channelName string) IChannel
	RegisterChannel(channelName string, channel IServerChannel) error
}

// IChannel interface for IPC channels
type IChannel interface {
	Call(ctx context.Context, command string, args ...interface{}) (interface{}, error)
	Listen(event string) (<-chan interface{}, error)
}

// IServerChannel interface for server-side IPC channels
type IServerChannel interface {
	Call(ctx context.Context, command string, args ...interface{}) (interface{}, error)
	Listen(ctx context.Context, event string) (<-chan interface{}, error)
}

// StorageScope represents the scope of storage
type StorageScope int

const (
	StorageScopeGlobal StorageScope = iota
	StorageScopeWorkspace
	StorageScopeProfile
)

// StorageTarget represents the target of storage
type StorageTarget int

const (
	StorageTargetUser StorageTarget = iota
	StorageTargetMachine
)

// StorageItem represents a storage item
type StorageItem struct {
	Key    string
	Value  string
	Scope  StorageScope
	Target StorageTarget
}

// IUserDataProfile represents a user data profile
type IUserDataProfile interface {
	GetID() string
	GetName() string
	GetLocation() string
	IsDefault() bool
}

// UserDataProfile implements IUserDataProfile
type UserDataProfile struct {
	ID       string
	Name     string
	Location string
	Default  bool
}

func (p *UserDataProfile) GetID() string       { return p.ID }
func (p *UserDataProfile) GetName() string     { return p.Name }
func (p *UserDataProfile) GetLocation() string { return p.Location }
func (p *UserDataProfile) IsDefault() bool     { return p.Default }

// IStorageValue represents a storage value with target
type IStorageValue struct {
	Value  string
	Target StorageTarget
}

// IStorageValueChangeEvent represents a storage value change event
type IStorageValueChangeEvent struct {
	Key    string
	Value  string
	Target StorageTarget
}

// IProfileStorageValueChanges represents profile storage value changes
type IProfileStorageValueChanges struct {
	Profile IUserDataProfile
	Changes []IStorageValueChangeEvent
}

// IProfileStorageChanges represents profile storage changes
type IProfileStorageChanges struct {
	TargetChanges []IUserDataProfile
	ValueChanges  []IProfileStorageValueChanges
}

// IUserDataProfileStorageService interface for user data profile storage
type IUserDataProfileStorageService interface {
	OnDidChange() <-chan IProfileStorageChanges
	ReadStorageData(profile IUserDataProfile) (map[string]IStorageValue, error)
	UpdateStorageData(profile IUserDataProfile, data map[string]*string, target StorageTarget) error
	WithProfileScopedStorageService(profile IUserDataProfile, fn func(IStorageService) error) error
}

// RemoteUserDataProfileStorageService base class for remote storage services
type RemoteUserDataProfileStorageService struct {
	persistStorages         bool
	mainProcessService      IMainProcessService
	userDataProfilesService IUserDataProfilesService
	storageService          IStorageService
	logService              ILogService
	onDidChangeChannel      chan IProfileStorageChanges
	storageServicesMap      map[string]IStorageService
}

// NewRemoteUserDataProfileStorageService creates a new RemoteUserDataProfileStorageService
func NewRemoteUserDataProfileStorageService(
	persistStorages bool,
	mainProcessService IMainProcessService,
	userDataProfilesService IUserDataProfilesService,
	storageService IStorageService,
	logService ILogService,
) *RemoteUserDataProfileStorageService {
	service := &RemoteUserDataProfileStorageService{
		persistStorages:         persistStorages,
		mainProcessService:      mainProcessService,
		userDataProfilesService: userDataProfilesService,
		storageService:          storageService,
		logService:              logService,
		onDidChangeChannel:      make(chan IProfileStorageChanges, 100),
	}

	if persistStorages {
		service.storageServicesMap = make(map[string]IStorageService)
	}

	return service
}

// OnDidChange returns a channel for storage change events
func (r *RemoteUserDataProfileStorageService) OnDidChange() <-chan IProfileStorageChanges {
	return r.onDidChangeChannel
}

// ReadStorageData reads storage data for a profile
func (r *RemoteUserDataProfileStorageService) ReadStorageData(profile IUserDataProfile) (map[string]IStorageValue, error) {
	result, err := r.WithProfileScopedStorageServiceResult(profile, func(storageService IStorageService) (interface{}, error) {
		return r.getItems(storageService), nil
	})
	if err != nil {
		return nil, err
	}
	return result.(map[string]IStorageValue), nil
}

// UpdateStorageData updates storage data for a profile
func (r *RemoteUserDataProfileStorageService) UpdateStorageData(profile IUserDataProfile, data map[string]*string, target StorageTarget) error {
	return r.WithProfileScopedStorageService(profile, func(storageService IStorageService) error {
		return r.writeItems(storageService, data, target)
	})
}

// WithProfileScopedStorageService executes a function with a profile-scoped storage service
func (r *RemoteUserDataProfileStorageService) WithProfileScopedStorageService(profile IUserDataProfile, fn func(IStorageService) error) error {
	_, err := r.WithProfileScopedStorageServiceResult(profile, func(storageService IStorageService) (interface{}, error) {
		return nil, fn(storageService)
	})
	return err
}

// WithProfileScopedStorageServiceResult executes a function with a profile-scoped storage service and returns a result
func (r *RemoteUserDataProfileStorageService) WithProfileScopedStorageServiceResult(profile IUserDataProfile, fn func(IStorageService) (interface{}, error)) (interface{}, error) {
	if r.storageService.HasScope(profile) {
		return fn(r.storageService)
	}

	var storageService IStorageService
	var exists bool

	if r.storageServicesMap != nil {
		storageService, exists = r.storageServicesMap[profile.GetID()]
	}

	if !exists {
		// In a real implementation, this would create a storage database for the profile
		// For now, we'll use the main storage service
		storageService = r.storageService
		if r.storageServicesMap != nil {
			r.storageServicesMap[profile.GetID()] = storageService
		}
	}

	result, err := fn(storageService)
	if err != nil && r.storageServicesMap != nil {
		delete(r.storageServicesMap, profile.GetID())
	}

	return result, err
}

// getItems retrieves all items from storage
func (r *RemoteUserDataProfileStorageService) getItems(storageService IStorageService) map[string]IStorageValue {
	result := make(map[string]IStorageValue)

	// Get items for both user and machine targets
	for _, target := range []StorageTarget{StorageTargetUser, StorageTargetMachine} {
		keys := storageService.Keys(StorageScopeProfile, target)
		for _, key := range keys {
			if value, err := storageService.Get(key, StorageScopeProfile); err == nil {
				result[key] = IStorageValue{Value: value, Target: target}
			}
		}
	}

	return result
}

// writeItems writes items to storage
func (r *RemoteUserDataProfileStorageService) writeItems(storageService IStorageService, items map[string]*string, target StorageTarget) error {
	var storageItems []StorageItem
	for key, value := range items {
		var val string
		if value != nil {
			val = *value
		}
		storageItems = append(storageItems, StorageItem{
			Key:    key,
			Value:  val,
			Scope:  StorageScopeProfile,
			Target: target,
		})
	}
	return storageService.StoreAll(storageItems, true)
}

// SharedProcessUserDataProfileStorageService extends RemoteUserDataProfileStorageService
type SharedProcessUserDataProfileStorageService struct {
	*RemoteUserDataProfileStorageService
}

// NewSharedProcessUserDataProfileStorageService creates a new SharedProcessUserDataProfileStorageService
func NewSharedProcessUserDataProfileStorageService(
	mainProcessService IMainProcessService,
	userDataProfilesService IUserDataProfilesService,
	storageService IStorageService,
	logService ILogService,
) *SharedProcessUserDataProfileStorageService {
	return &SharedProcessUserDataProfileStorageService{
		RemoteUserDataProfileStorageService: NewRemoteUserDataProfileStorageService(
			true, // persistStorages
			mainProcessService,
			userDataProfilesService,
			storageService,
			logService,
		),
	}
}

// String returns a string representation of the service
func (s *SharedProcessUserDataProfileStorageService) String() string {
	return fmt.Sprintf("SharedProcessUserDataProfileStorageService{persistStorages: %v}", s.persistStorages)
}
