/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"sort"
	"strings"

	baseCommon "vscode-go/src/vs/base/common"
	"vscode-go/src/vs/base/common/glob"
)

// IWatchRequest represents a watch request
type IWatchRequest struct {
	Path          string            `json:"path"`
	Recursive     bool              `json:"recursive"`
	Excludes      []string          `json:"excludes"`
	Includes      []interface{}     `json:"includes"` // Array of string or IRelativePattern
	CorrelationId *int              `json:"correlationId"`
	Filter        *FileChangeFilter `json:"filter"`
}

// IWatchRequestWithCorrelation represents a watch request with correlation
type IWatchRequestWithCorrelation struct {
	IWatchRequest
	CorrelationId int `json:"correlationId"`
}

// IsWatchRequestWithCorrelation checks if a request has correlation
func IsWatchRequestWithCorrelation(request IWatchRequest) bool {
	return request.CorrelationId != nil
}

// INonRecursiveWatchRequest represents a non-recursive watch request
type INonRecursiveWatchRequest struct {
	IWatchRequest
	Recursive bool `json:"recursive"` // always false
}

// IRecursiveWatchRequest represents a recursive watch request
type IRecursiveWatchRequest struct {
	IWatchRequest
	Recursive       bool `json:"recursive"`       // always true
	PollingInterval *int `json:"pollingInterval"` // deprecated, for WSL1 support only
}

// IsRecursiveWatchRequest checks if a request is recursive
func IsRecursiveWatchRequest(request IWatchRequest) bool {
	return request.Recursive
}

// IUniversalWatchRequest is a union type for recursive and non-recursive requests
type IUniversalWatchRequest interface {
	GetPath() string
	GetRecursive() bool
	GetExcludes() []string
	GetIncludes() []interface{}
	GetCorrelationId() *int
	GetFilter() *FileChangeFilter
}

// IWatcherErrorEvent represents a watcher error event
type IWatcherErrorEvent struct {
	Error   string                  `json:"error"`
	Request *IUniversalWatchRequest `json:"request"`
}

// IWatcher represents a file watcher interface
type IWatcher interface {
	OnDidChangeFile() baseCommon.Event[[]IFileChange]
	OnDidLogMessage() baseCommon.Event[ILogMessage]
	OnDidError() baseCommon.Event[IWatcherErrorEvent]
	Watch(requests []IWatchRequest) error
	SetVerboseLogging(enabled bool) error
	Stop() error
}

// IRecursiveWatcher represents a recursive file watcher
type IRecursiveWatcher interface {
	IWatcher
	WatchRecursive(requests []IRecursiveWatchRequest) error
}

// IRecursiveWatcherWithSubscribe represents a recursive watcher with subscription support
type IRecursiveWatcherWithSubscribe interface {
	IRecursiveWatcher
	Subscribe(path string, callback func(error bool, change *IFileChange)) baseCommon.IDisposable
}

// IRecursiveWatcherOptions represents options for recursive watchers
type IRecursiveWatcherOptions struct {
	UsePolling      interface{} `json:"usePolling"`      // bool or []string
	PollingInterval *int        `json:"pollingInterval"` // deprecated
}

// INonRecursiveWatcher represents a non-recursive file watcher
type INonRecursiveWatcher interface {
	IWatcher
	WatchNonRecursive(requests []INonRecursiveWatchRequest) error
}

// IUniversalWatcher represents a universal file watcher
type IUniversalWatcher interface {
	IWatcher
	WatchUniversal(requests []IUniversalWatchRequest) error
}

// ILogMessage represents a log message
type ILogMessage struct {
	Type    string `json:"type"` // 'trace' | 'warn' | 'error' | 'info' | 'debug'
	Message string `json:"message"`
}

// ReviveFileChanges revives file changes from serialized form
func ReviveFileChanges(changes []IFileChange) []IFileChange {
	result := make([]IFileChange, len(changes))
	for i, change := range changes {
		result[i] = IFileChange{
			Type:     change.Type,
			Resource: change.Resource, // Already a pointer, no need to revive
			CId:      change.CId,
		}
	}
	return result
}

// CoalesceEvents coalesces file change events
func CoalesceEvents(changes []IFileChange) []IFileChange {
	coalescer := NewEventCoalescer()
	for _, event := range changes {
		coalescer.ProcessEvent(event)
	}
	return coalescer.Coalesce()
}

// NormalizeWatcherPattern normalizes a watcher pattern
func NormalizeWatcherPattern(path string, pattern interface{}) interface{} {
	if str, ok := pattern.(string); ok {
		if !strings.HasPrefix(str, "**") && !baseCommon.IsAbsolute(str) {
			return glob.IRelativePattern{
				Base:    path,
				Pattern: str,
			}
		}
	}
	return pattern
}

// ParseWatcherPatterns parses watcher patterns
func ParseWatcherPatterns(path string, patterns []interface{}) []interface{} {
	var parsedPatterns []interface{}
	for _, pattern := range patterns {
		normalized := NormalizeWatcherPattern(path, pattern)
		parsedPatterns = append(parsedPatterns, normalized)
	}
	return parsedPatterns
}

// EventCoalescer coalesces file change events
type EventCoalescer struct {
	coalesced       map[string]IFileChange
	mapPathToChange map[string]IFileChange
}

// NewEventCoalescer creates a new event coalescer
func NewEventCoalescer() *EventCoalescer {
	return &EventCoalescer{
		coalesced:       make(map[string]IFileChange),
		mapPathToChange: make(map[string]IFileChange),
	}
}

// toKey generates a key for the event
func (ec *EventCoalescer) toKey(event IFileChange) string {
	if baseCommon.IsLinux {
		return event.Resource.GetFSPath()
	}
	return strings.ToLower(event.Resource.GetFSPath())
}

// ProcessEvent processes a file change event
func (ec *EventCoalescer) ProcessEvent(event IFileChange) {
	key := ec.toKey(event)
	existingEvent, exists := ec.mapPathToChange[key]

	keepEvent := false

	if exists {
		currentChangeType := existingEvent.Type
		newChangeType := event.Type

		// Handle case sensitivity renames
		if existingEvent.Resource.GetFSPath() != event.Resource.GetFSPath() &&
			(event.Type == FileChangeTypeDeleted || event.Type == FileChangeTypeAdded) {
			keepEvent = true
		} else if currentChangeType == FileChangeTypeAdded && newChangeType == FileChangeTypeDeleted {
			// Ignore CREATE followed by DELETE
			delete(ec.mapPathToChange, key)
			delete(ec.coalesced, key)
		} else if currentChangeType == FileChangeTypeDeleted && newChangeType == FileChangeTypeAdded {
			// Flatten DELETE followed by CREATE into CHANGE
			existingEvent.Type = FileChangeTypeUpdated
			ec.mapPathToChange[key] = existingEvent
			ec.coalesced[key] = existingEvent
		} else if currentChangeType == FileChangeTypeAdded && newChangeType == FileChangeTypeUpdated {
			// Keep the created event
		} else {
			// Apply change type
			existingEvent.Type = newChangeType
			ec.mapPathToChange[key] = existingEvent
			ec.coalesced[key] = existingEvent
		}
	} else {
		keepEvent = true
	}

	if keepEvent {
		ec.coalesced[key] = event
		ec.mapPathToChange[key] = event
	}
}

// Coalesce returns the coalesced events
func (ec *EventCoalescer) Coalesce() []IFileChange {
	var events []IFileChange
	var deletedPaths []string

	// Separate ADD/CHANGE and DELETE events
	var addOrChangeEvents []IFileChange
	var deleteEvents []IFileChange

	for _, event := range ec.coalesced {
		if event.Type != FileChangeTypeDeleted {
			addOrChangeEvents = append(addOrChangeEvents, event)
		} else {
			deleteEvents = append(deleteEvents, event)
		}
	}

	// Sort delete events by path length (shortest first)
	sort.Slice(deleteEvents, func(i, j int) bool {
		return len(deleteEvents[i].Resource.GetFSPath()) < len(deleteEvents[j].Resource.GetFSPath())
	})

	// Filter delete events to remove children of already deleted parents
	for _, event := range deleteEvents {
		isChildOfDeleted := false
		for _, deletedPath := range deletedPaths {
			if IsParent(event.Resource.GetFSPath(), deletedPath, !baseCommon.IsLinux) {
				isChildOfDeleted = true
				break
			}
		}

		if !isChildOfDeleted {
			deletedPaths = append(deletedPaths, event.Resource.GetFSPath())
			events = append(events, event)
		}
	}

	// Add the ADD/CHANGE events
	events = append(events, addOrChangeEvents...)

	return events
}

// IsFiltered checks if an event is filtered
func IsFiltered(event IFileChange, filter *FileChangeFilter) bool {
	if filter == nil {
		return false
	}

	switch event.Type {
	case FileChangeTypeAdded:
		return (*filter & FileChangeFilterAdded) == 0
	case FileChangeTypeDeleted:
		return (*filter & FileChangeFilterDeleted) == 0
	case FileChangeTypeUpdated:
		return (*filter & FileChangeFilterUpdated) == 0
	}

	return false
}

// RequestFilterToString converts a filter to string representation
func RequestFilterToString(filter *FileChangeFilter) string {
	if filter == nil {
		return "<none>"
	}

	var filters []string
	if *filter&FileChangeFilterAdded != 0 {
		filters = append(filters, "Added")
	}
	if *filter&FileChangeFilterDeleted != 0 {
		filters = append(filters, "Deleted")
	}
	if *filter&FileChangeFilterUpdated != 0 {
		filters = append(filters, "Updated")
	}

	if len(filters) == 0 {
		return "<all>"
	}

	return "[" + strings.Join(filters, ", ") + "]"
}

// IsParent checks if childPath is a child of parentPath
func IsParent(childPath, parentPath string, ignoreCase bool) bool {
	if ignoreCase {
		childPath = strings.ToLower(childPath)
		parentPath = strings.ToLower(parentPath)
	}

	if !strings.HasSuffix(parentPath, "/") {
		parentPath += "/"
	}

	return strings.HasPrefix(childPath, parentPath)
}
