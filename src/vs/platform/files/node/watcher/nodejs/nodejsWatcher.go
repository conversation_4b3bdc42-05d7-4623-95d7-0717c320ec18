/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package nodejs

import (
	"fmt"
	"runtime"
	"strings"
	"sync"

	baseCommon "vscode-go/src/vs/base/common"
	"vscode-go/src/vs/base/common/async"
	"vscode-go/src/vs/base/common/event"
	"vscode-go/src/vs/base/common/glob"
	"vscode-go/src/vs/base/common/lifecycle"
	filesCommon "vscode-go/src/vs/platform/files/common"
	"vscode-go/src/vs/platform/files/node/watcher"
)

// INodeJSWatcherInstance represents a NodeJS watcher instance
type INodeJSWatcherInstance struct {
	Instance *NodeJSFileWatcherLibrary           `json:"instance"`
	Request  filesCommon.INonRecursiveWatchRequest `json:"request"`
}

// NodeJSWatcher implements a non-recursive file watcher using NodeJS
type NodeJSWatcher struct {
	*watcher.BaseWatcher
	
	onDidError event.Event[filesCommon.IWatcherErrorEvent]
	
	watchers map[interface{}]*INodeJSWatcherInstance // key can be string (path) or int (correlation ID)
	worker   *lifecycle.MutableDisposable[*async.ThrottledWorker[filesCommon.INonRecursiveWatchRequest]]
	
	recursiveWatcher filesCommon.IRecursiveWatcherWithSubscribe
	
	mutex sync.RWMutex
}

// NewNodeJSWatcher creates a new NodeJS watcher
func NewNodeJSWatcher(recursiveWatcher filesCommon.IRecursiveWatcherWithSubscribe) *NodeJSWatcher {
	nw := &NodeJSWatcher{
		BaseWatcher:      watcher.NewBaseWatcher(),
		onDidError:       event.None[filesCommon.IWatcherErrorEvent](),
		watchers:         make(map[interface{}]*INodeJSWatcherInstance),
		worker:           lifecycle.NewMutableDisposable[*async.ThrottledWorker[filesCommon.INonRecursiveWatchRequest]](),
		recursiveWatcher: recursiveWatcher,
	}
	
	nw.Register(nw.worker)
	
	return nw
}

// OnDidError returns the error event
func (nw *NodeJSWatcher) OnDidError() baseCommon.Event[filesCommon.IWatcherErrorEvent] {
	return nw.onDidError
}

// Watchers returns an iterator over all watcher instances
func (nw *NodeJSWatcher) Watchers() []*INodeJSWatcherInstance {
	nw.mutex.RLock()
	defer nw.mutex.RUnlock()
	
	var result []*INodeJSWatcherInstance
	for _, watcher := range nw.watchers {
		result = append(result, watcher)
	}
	return result
}

// doWatch implements the abstract doWatch method from BaseWatcher
func (nw *NodeJSWatcher) doWatch(requests []filesCommon.IUniversalWatchRequest) error {
	// Convert to non-recursive requests
	var nonRecursiveRequests []filesCommon.INonRecursiveWatchRequest
	for _, req := range requests {
		if !req.GetRecursive() {
			if nonRecReq, ok := req.(filesCommon.INonRecursiveWatchRequest); ok {
				nonRecursiveRequests = append(nonRecursiveRequests, nonRecReq)
			}
		}
	}
	
	// Remove duplicates
	nonRecursiveRequests = nw.removeDuplicateRequests(nonRecursiveRequests)
	
	// Figure out which watchers to start and stop
	var requestsToStart []filesCommon.INonRecursiveWatchRequest
	watchersToStop := make(map[*INodeJSWatcherInstance]bool)
	
	// Mark all current watchers for potential stopping
	for _, watcher := range nw.Watchers() {
		watchersToStop[watcher] = true
	}
	
	// Check each request
	for _, request := range nonRecursiveRequests {
		key := nw.requestToWatcherKey(request)
		if watcher, exists := nw.watchers[key]; exists {
			// Check if patterns match
			if glob.PatternsEquals(watcher.Request.Excludes, request.Excludes) &&
			   glob.PatternsEquals(watcher.Request.Includes, request.Includes) {
				delete(watchersToStop, watcher) // keep this watcher
			} else {
				requestsToStart = append(requestsToStart, request) // restart with new patterns
			}
		} else {
			requestsToStart = append(requestsToStart, request) // start new watcher
		}
	}
	
	// Logging
	if len(requestsToStart) > 0 {
		var requestStrings []string
		for _, req := range requestsToStart {
			requestStrings = append(requestStrings, nw.requestToString(req))
		}
		nw.trace(fmt.Sprintf("Request to start watching: %s", strings.Join(requestStrings, ",")))
	}
	
	if len(watchersToStop) > 0 {
		var requestStrings []string
		for watcher := range watchersToStop {
			requestStrings = append(requestStrings, nw.requestToString(watcher.Request))
		}
		nw.trace(fmt.Sprintf("Request to stop watching: %s", strings.Join(requestStrings, ",")))
	}
	
	// Stop the worker
	nw.worker.Clear()
	
	// Stop watchers as instructed
	for watcher := range watchersToStop {
		nw.stopWatching(watcher)
	}
	
	// Start watching as instructed
	nw.createWatchWorker().Work(requestsToStart)
	
	return nil
}

// createWatchWorker creates a throttled worker for starting watchers
func (nw *NodeJSWatcher) createWatchWorker() *async.ThrottledWorker[filesCommon.INonRecursiveWatchRequest] {
	worker := async.NewThrottledWorker[filesCommon.INonRecursiveWatchRequest](
		async.ThrottledWorkerOptions{
			MaxWorkChunkSize: 100,                // only start 100 watchers at once
			ThrottleDelay:    100,                // rest for 100ms
			MaxBufferedWork:  int(^uint(0) >> 1), // max int value - never refuse work
		},
		func(requests []filesCommon.INonRecursiveWatchRequest) {
			for _, request := range requests {
				nw.startWatching(request)
			}
		},
	)
	
	nw.worker.Value = worker
	return worker
}

// requestToWatcherKey generates a key for the watcher map
func (nw *NodeJSWatcher) requestToWatcherKey(request filesCommon.INonRecursiveWatchRequest) interface{} {
	if request.CorrelationId != nil {
		return *request.CorrelationId
	}
	return nw.pathToWatcherKey(request.Path)
}

// pathToWatcherKey generates a key for a path
func (nw *NodeJSWatcher) pathToWatcherKey(path string) string {
	if runtime.GOOS == "linux" {
		return path
	}
	return strings.ToLower(path) // ignore path casing on non-Linux systems
}

// startWatching starts watching a request
func (nw *NodeJSWatcher) startWatching(request filesCommon.INonRecursiveWatchRequest) {
	// Create the watcher instance
	instance := NewNodeJSFileWatcherLibrary(
		request,
		nw.recursiveWatcher,
		func(changes []filesCommon.IFileChange) {
			nw.OnDidChangeFile().Fire(changes)
		},
		func() {
			nw.OnDidWatchFail().Fire(request)
		},
		func(msg filesCommon.ILogMessage) {
			nw.OnDidLogMessage().Fire(msg)
		},
		nw.VerboseLogging,
	)
	
	// Create watcher instance wrapper
	watcherInstance := &INodeJSWatcherInstance{
		Request:  request,
		Instance: instance,
	}
	
	// Store in map
	nw.mutex.Lock()
	nw.watchers[nw.requestToWatcherKey(request)] = watcherInstance
	nw.mutex.Unlock()
}

// Stop stops all watchers
func (nw *NodeJSWatcher) Stop() error {
	err := nw.BaseWatcher.Stop()
	if err != nil {
		return err
	}
	
	for _, watcher := range nw.Watchers() {
		nw.stopWatching(watcher)
	}
	
	return nil
}

// stopWatching stops a specific watcher
func (nw *NodeJSWatcher) stopWatching(watcher *INodeJSWatcherInstance) {
	nw.trace(fmt.Sprintf("stopping file watcher: %+v", watcher))
	
	nw.mutex.Lock()
	delete(nw.watchers, nw.requestToWatcherKey(watcher.Request))
	nw.mutex.Unlock()
	
	watcher.Instance.Dispose()
}

// removeDuplicateRequests removes duplicate requests
func (nw *NodeJSWatcher) removeDuplicateRequests(requests []filesCommon.INonRecursiveWatchRequest) []filesCommon.INonRecursiveWatchRequest {
	correlationToRequests := make(map[interface{}]map[string]filesCommon.INonRecursiveWatchRequest)
	
	// Group by correlation ID
	for _, request := range requests {
		var correlation interface{}
		if request.CorrelationId != nil {
			correlation = *request.CorrelationId
		}
		
		requestsForCorrelation, exists := correlationToRequests[correlation]
		if !exists {
			requestsForCorrelation = make(map[string]filesCommon.INonRecursiveWatchRequest)
			correlationToRequests[correlation] = requestsForCorrelation
		}
		
		path := nw.pathToWatcherKey(request.Path)
		if _, exists := requestsForCorrelation[path]; exists {
			nw.trace(fmt.Sprintf("ignoring a request for watching whose path is already watched: %s", nw.requestToString(request)))
		}
		
		requestsForCorrelation[path] = request
	}
	
	// Flatten back to array
	var result []filesCommon.INonRecursiveWatchRequest
	for _, requestsMap := range correlationToRequests {
		for _, request := range requestsMap {
			result = append(result, request)
		}
	}
	
	return result
}

// SetVerboseLogging sets verbose logging for all watchers
func (nw *NodeJSWatcher) SetVerboseLogging(enabled bool) error {
	err := nw.BaseWatcher.SetVerboseLogging(enabled)
	if err != nil {
		return err
	}
	
	for _, watcher := range nw.Watchers() {
		watcher.Instance.SetVerboseLogging(enabled)
	}
	
	return nil
}

// trace logs a trace message
func (nw *NodeJSWatcher) trace(message string, watcher ...*INodeJSWatcherInstance) {
	if nw.VerboseLogging {
		var finalMessage string
		if len(watcher) > 0 && watcher[0] != nil {
			finalMessage = nw.toMessage(message, watcher[0])
		} else {
			finalMessage = nw.toMessage(message, nil)
		}
		
		nw.OnDidLogMessage().Fire(filesCommon.ILogMessage{
			Type:    "trace",
			Message: finalMessage,
		})
	}
}

// warn logs a warning message
func (nw *NodeJSWatcher) warn(message string) {
	nw.OnDidLogMessage().Fire(filesCommon.ILogMessage{
		Type:    "warn",
		Message: nw.toMessage(message, nil),
	})
}

// toMessage formats a message with optional watcher context
func (nw *NodeJSWatcher) toMessage(message string, watcher *INodeJSWatcherInstance) string {
	if watcher != nil {
		return fmt.Sprintf("[File Watcher (node.js)] %s (%s)", message, nw.requestToString(watcher.Request))
	}
	return fmt.Sprintf("[File Watcher (node.js)] %s", message)
}

// requestToString converts a request to string representation
func (nw *NodeJSWatcher) requestToString(request interface{}) string {
	// This would use the BaseWatcher's requestToString method
	// For now, return a simple string representation
	return fmt.Sprintf("%+v", request)
}
