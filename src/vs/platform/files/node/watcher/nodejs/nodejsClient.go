/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package nodejs

import (
	"vscode-go/src/vs/base/common/lifecycle"
	filesCommon "vscode-go/src/vs/platform/files/common"
)

// NodeJSWatcherClient implements a non-recursive watcher client using NodeJS
type NodeJSWatcherClient struct {
	*filesCommon.AbstractNonRecursiveWatcherClient
}

// NewNodeJSWatcherClient creates a new NodeJS watcher client
func NewNodeJSWatcherClient(
	onFileChanges func([]filesCommon.IFileChange),
	onLogMessage func(filesCommon.ILogMessage),
	verboseLogging bool,
) *NodeJSWatcherClient {
	client := &NodeJSWatcherClient{}
	
	// Initialize the abstract client
	client.AbstractNonRecursiveWatcherClient = filesCommon.NewAbstractNonRecursiveWatcherClient(
		onFileChanges,
		onLogMessage,
		verboseLogging,
	)
	
	// Initialize the watcher
	client.Init()
	
	return client
}

// CreateWatcher creates the underlying watcher instance
func (client *NodeJSWatcherClient) CreateWatcher(disposables *lifecycle.DisposableStore) filesCommon.INonRecursiveWatcher {
	watcher := NewNodeJSWatcher(nil) // no recursive watching support here
	disposables.Add(watcher)
	return watcher
}
