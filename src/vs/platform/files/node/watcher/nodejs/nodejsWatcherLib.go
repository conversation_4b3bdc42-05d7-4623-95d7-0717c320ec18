/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package nodejs

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"

	baseCommon "vscode-go/src/vs/base/common"
	"vscode-go/src/vs/base/common/async"
	"vscode-go/src/vs/base/common/cancellation"
	"vscode-go/src/vs/base/common/lazy"
	"vscode-go/src/vs/base/common/lifecycle"
	"vscode-go/src/vs/base/common/normalization"
	"vscode-go/src/vs/base/node/pfs"
	filesCommon "vscode-go/src/vs/platform/files/common"
)

// NodeJSFileWatcherLibrary implements file watching using fsnotify
type NodeJSFileWatcherLibrary struct {
	lifecycle.Disposable

	// Constants
	fileDeleteHandlerDelay  time.Duration
	fileChangesHandlerDelay time.Duration

	// Throttled file changes emitter
	throttledFileChangesEmitter *async.ThrottledWorker[filesCommon.IFileChange]

	// File changes aggregator
	fileChangesAggregator *async.RunOnceWorker[filesCommon.IFileChange]

	// Parsed patterns
	excludes []interface{}
	includes []interface{}
	filter   *filesCommon.FileChangeFilter

	// Cancellation
	cts *cancellation.CancellationTokenSource

	// Real path lazy evaluation
	realPath *lazy.Lazy[string]

	// Ready promise
	ready chan struct{}

	// State
	isReusingRecursiveWatcher bool
	didFail                   bool

	// Dependencies
	request          filesCommon.INonRecursiveWatchRequest
	recursiveWatcher filesCommon.IRecursiveWatcherWithSubscribe
	onDidFilesChange func([]filesCommon.IFileChange)
	onDidWatchFail   func()
	onLogMessage     func(filesCommon.ILogMessage)
	verboseLogging   bool

	// File watcher
	watcher *fsnotify.Watcher
	mutex   sync.RWMutex
}

// NewNodeJSFileWatcherLibrary creates a new NodeJS file watcher library instance
func NewNodeJSFileWatcherLibrary(
	request filesCommon.INonRecursiveWatchRequest,
	recursiveWatcher filesCommon.IRecursiveWatcherWithSubscribe,
	onDidFilesChange func([]filesCommon.IFileChange),
	onDidWatchFail func(),
	onLogMessage func(filesCommon.ILogMessage),
	verboseLogging bool,
) *NodeJSFileWatcherLibrary {

	lib := &NodeJSFileWatcherLibrary{
		fileDeleteHandlerDelay:  100 * time.Millisecond,
		fileChangesHandlerDelay: 75 * time.Millisecond,
		request:                 request,
		recursiveWatcher:        recursiveWatcher,
		onDidFilesChange:        onDidFilesChange,
		onDidWatchFail:          onDidWatchFail,
		onLogMessage:            onLogMessage,
		verboseLogging:          verboseLogging,
		ready:                   make(chan struct{}),
		cts:                     cancellation.NewCancellationTokenSource(),
	}

	// Initialize throttled file changes emitter
	lib.throttledFileChangesEmitter = async.NewThrottledWorker[filesCommon.IFileChange](
		async.ThrottledWorkerOptions{
			MaxWorkChunkSize: 100,
			ThrottleDelay:    200 * time.Millisecond,
			MaxBufferedWork:  10000,
		},
		func(events []filesCommon.IFileChange) {
			lib.onDidFilesChange(events)
		},
	)

	// Initialize file changes aggregator
	lib.fileChangesAggregator = async.NewRunOnceWorker[filesCommon.IFileChange](
		func(events []filesCommon.IFileChange) {
			lib.handleFileChanges(events)
		},
		lib.fileChangesHandlerDelay,
	)

	// Parse patterns
	lib.excludes = filesCommon.ParseWatcherPatterns(request.Path, request.Excludes)
	if request.Includes != nil {
		lib.includes = filesCommon.ParseWatcherPatterns(request.Path, request.Includes)
	}

	// Set filter if request has correlation
	if filesCommon.IsWatchRequestWithCorrelation(request.IWatchRequest) {
		lib.filter = request.Filter
	}

	// Initialize real path lazy evaluation
	lib.realPath = lazy.NewLazy[string](func() string {
		result := request.Path

		realPath, err := pfs.Realpath(request.Path)
		if err == nil && request.Path != realPath {
			lib.trace(fmt.Sprintf("correcting a path to watch that seems to be a symbolic link (original: %s, real: %s)", request.Path, realPath))
			result = realPath
		}

		return result
	})

	// Start watching
	go lib.watch()

	return lib
}

// Ready returns a channel that is closed when the watcher is ready
func (lib *NodeJSFileWatcherLibrary) Ready() <-chan struct{} {
	return lib.ready
}

// IsReusingRecursiveWatcher returns whether this watcher is reusing a recursive watcher
func (lib *NodeJSFileWatcherLibrary) IsReusingRecursiveWatcher() bool {
	lib.mutex.RLock()
	defer lib.mutex.RUnlock()
	return lib.isReusingRecursiveWatcher
}

// Failed returns whether the watcher has failed
func (lib *NodeJSFileWatcherLibrary) Failed() bool {
	lib.mutex.RLock()
	defer lib.mutex.RUnlock()
	return lib.didFail
}

// watch starts the file watching process
func (lib *NodeJSFileWatcherLibrary) watch() {
	defer close(lib.ready)

	stat, err := os.Stat(lib.request.Path)
	if err != nil {
		if !os.IsNotExist(err) {
			lib.error(fmt.Sprintf("Failed to stat path: %v", err))
		} else {
			lib.trace(fmt.Sprintf("ignoring a path for watching whose stat info failed to resolve: %s (error: %v)", lib.request.Path, err))
		}
		lib.notifyWatchFailed()
		return
	}

	if lib.cts.Token().IsCancellationRequested() {
		return
	}

	disposable, err := lib.doWatch(stat.IsDir())
	if err != nil {
		lib.error(fmt.Sprintf("Failed to start watching: %v", err))
		lib.notifyWatchFailed()
		return
	}

	lib.Register(disposable)
}

// notifyWatchFailed notifies that watching has failed
func (lib *NodeJSFileWatcherLibrary) notifyWatchFailed() {
	lib.mutex.Lock()
	lib.didFail = true
	lib.mutex.Unlock()

	if lib.onDidWatchFail != nil {
		lib.onDidWatchFail()
	}
}

// doWatch performs the actual watching
func (lib *NodeJSFileWatcherLibrary) doWatch(isDirectory bool) (lifecycle.IDisposable, error) {
	disposables := lifecycle.NewDisposableStore()

	if lib.doWatchWithExistingWatcher(isDirectory, disposables) {
		lib.trace(fmt.Sprintf("reusing an existing recursive watcher for %s", lib.request.Path))
		lib.mutex.Lock()
		lib.isReusingRecursiveWatcher = true
		lib.mutex.Unlock()
	} else {
		lib.mutex.Lock()
		lib.isReusingRecursiveWatcher = false
		lib.mutex.Unlock()

		err := lib.doWatchWithFSNotify(isDirectory, disposables)
		if err != nil {
			disposables.Dispose()
			return nil, err
		}
	}

	return disposables, nil
}

// doWatchWithExistingWatcher attempts to reuse an existing recursive watcher
func (lib *NodeJSFileWatcherLibrary) doWatchWithExistingWatcher(isDirectory bool, disposables *lifecycle.DisposableStore) bool {
	if isDirectory {
		// Recursive watcher re-use is currently not enabled for folders
		return false
	}

	if lib.recursiveWatcher == nil {
		return false
	}

	resource := baseCommon.NewURI("file", "", lib.request.Path, "", "")
	subscription := lib.recursiveWatcher.Subscribe(lib.request.Path, func(error bool, change *filesCommon.IFileChange) {
		if disposables.IsDisposed() {
			return
		}

		if error {
			// Fallback to fsnotify watching
			watchDisposable, err := lib.doWatch(isDirectory)
			if err == nil && !disposables.IsDisposed() {
				disposables.Add(watchDisposable)
			} else if watchDisposable != nil {
				watchDisposable.Dispose()
			}
		} else if change != nil {
			correlationId := lib.request.CorrelationId
			if correlationId != nil || change.CId != nil {
				// Re-emit with correlation ID
				lib.onFileChange(filesCommon.IFileChange{
					Type:     change.Type,
					Resource: resource,
					CId:      correlationId,
				}, true)
			}
		}
	})

	if subscription != nil {
		disposables.Add(subscription)
		return true
	}

	return false
}

// doWatchWithFSNotify performs watching using fsnotify
func (lib *NodeJSFileWatcherLibrary) doWatchWithFSNotify(isDirectory bool, disposables *lifecycle.DisposableStore) error {
	realPath, err := lib.realPath.Value()
	if err != nil {
		return fmt.Errorf("failed to get real path: %w", err)
	}

	if lib.cts.Token().IsCancellationRequested() {
		return nil
	}

	// macOS: avoid watching samba shares
	if runtime.GOOS == "darwin" && strings.HasPrefix(realPath, "/Volumes/") {
		return fmt.Errorf("refusing to watch %s for changes using fsnotify for possibly being a network share where watching is unreliable and unstable", realPath)
	}

	// Create fsnotify watcher
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return fmt.Errorf("failed to create fsnotify watcher: %w", err)
	}

	lib.mutex.Lock()
	lib.watcher = watcher
	lib.mutex.Unlock()

	disposables.Add(lifecycle.ToDisposable(func() {
		lib.mutex.Lock()
		if lib.watcher != nil {
			lib.watcher.Close()
			lib.watcher = nil
		}
		lib.mutex.Unlock()
	}))

	// Add path to watcher
	err = watcher.Add(realPath)
	if err != nil {
		return fmt.Errorf("failed to add path to watcher: %w", err)
	}

	lib.trace(fmt.Sprintf("Started watching: '%s'", realPath))

	// Start event processing
	go lib.processEvents(watcher, isDirectory, realPath)

	return nil
}

// processEvents processes fsnotify events
func (lib *NodeJSFileWatcherLibrary) processEvents(watcher *fsnotify.Watcher, isDirectory bool, realPath string) {
	requestResource := baseCommon.NewURI("file", "", lib.request.Path, "", "")
	pathBasename := filepath.Base(realPath)

	// For directories, track children
	var folderChildren map[string]bool
	if isDirectory {
		folderChildren = make(map[string]bool)
		entries, err := os.ReadDir(realPath)
		if err == nil {
			for _, entry := range entries {
				folderChildren[entry.Name()] = true
			}
		}
	}

	for {
		select {
		case <-lib.cts.Token().Done():
			return
		case event, ok := <-watcher.Events:
			if !ok {
				return
			}

			lib.handleFSNotifyEvent(event, isDirectory, requestResource, pathBasename, folderChildren, realPath)

		case err, ok := <-watcher.Errors:
			if !ok {
				return
			}

			if !lib.cts.Token().IsCancellationRequested() {
				lib.error(fmt.Sprintf("Failed to watch %s for changes using fsnotify: %v", realPath, err))
				lib.notifyWatchFailed()
			}
			return
		}
	}
}

// handleFSNotifyEvent handles a single fsnotify event
func (lib *NodeJSFileWatcherLibrary) handleFSNotifyEvent(
	event fsnotify.Event,
	isDirectory bool,
	requestResource *baseCommon.URI,
	pathBasename string,
	folderChildren map[string]bool,
	realPath string,
) {
	if lib.cts.Token().IsCancellationRequested() {
		return
	}

	if lib.verboseLogging {
		lib.traceWithCorrelation(fmt.Sprintf("[raw] [%s] %s", event.Op.String(), event.Name))
	}

	// Normalize file name
	changedFileName := filepath.Base(event.Name)
	if runtime.GOOS == "darwin" {
		// Mac: uses NFD unicode form on disk, but we want NFC
		changedFileName = normalization.NormalizeNFC(changedFileName)
	}

	if changedFileName == "" {
		return
	}

	if isDirectory {
		lib.handleDirectoryEvent(event, changedFileName, requestResource, folderChildren, realPath, pathBasename)
	} else {
		lib.handleFileEvent(event, changedFileName, requestResource, pathBasename, realPath)
	}
}

// handleDirectoryEvent handles events for directories
func (lib *NodeJSFileWatcherLibrary) handleDirectoryEvent(
	event fsnotify.Event,
	changedFileName string,
	requestResource *baseCommon.URI,
	folderChildren map[string]bool,
	realPath string,
	pathBasename string,
) {
	if event.Op&fsnotify.Create != 0 || event.Op&fsnotify.Remove != 0 {
		// Handle file/folder creation/deletion
		go func() {
			time.Sleep(lib.fileDeleteHandlerDelay)

			if lib.cts.Token().IsCancellationRequested() {
				return
			}

			// Check if the changed file is the watched directory itself
			if changedFileName == pathBasename {
				if _, err := os.Stat(realPath); os.IsNotExist(err) {
					lib.onWatchedPathDeleted(requestResource)
					return
				}
			}

			// Check if file exists
			childPath := filepath.Join(realPath, changedFileName)
			_, err := os.Stat(childPath)
			fileExists := !os.IsNotExist(err)

			if lib.cts.Token().IsCancellationRequested() {
				return
			}

			// Determine event type
			var changeType filesCommon.FileChangeType
			if fileExists {
				if folderChildren[changedFileName] {
					changeType = filesCommon.FileChangeTypeUpdated
				} else {
					changeType = filesCommon.FileChangeTypeAdded
					folderChildren[changedFileName] = true
				}
			} else {
				delete(folderChildren, changedFileName)
				changeType = filesCommon.FileChangeTypeDeleted
			}

			childResource := baseCommon.NewURI("file", "", childPath, "", "")
			lib.onFileChange(filesCommon.IFileChange{
				Type:     changeType,
				Resource: childResource,
				CId:      lib.request.CorrelationId,
			}, false)
		}()
	} else if event.Op&fsnotify.Write != 0 {
		// Handle file modification
		var changeType filesCommon.FileChangeType
		if folderChildren[changedFileName] {
			changeType = filesCommon.FileChangeTypeUpdated
		} else {
			changeType = filesCommon.FileChangeTypeAdded
			folderChildren[changedFileName] = true
		}

		childPath := filepath.Join(realPath, changedFileName)
		childResource := baseCommon.NewURI("file", "", childPath, "", "")
		lib.onFileChange(filesCommon.IFileChange{
			Type:     changeType,
			Resource: childResource,
			CId:      lib.request.CorrelationId,
		}, false)
	}
}

// handleFileEvent handles events for files
func (lib *NodeJSFileWatcherLibrary) handleFileEvent(
	event fsnotify.Event,
	changedFileName string,
	requestResource *baseCommon.URI,
	pathBasename string,
	realPath string,
) {
	if event.Op&fsnotify.Remove != 0 || changedFileName != pathBasename {
		// File deleted or renamed
		go func() {
			time.Sleep(lib.fileDeleteHandlerDelay)

			if lib.cts.Token().IsCancellationRequested() {
				return
			}

			if _, err := os.Stat(realPath); os.IsNotExist(err) {
				// File deleted
				lib.onWatchedPathDeleted(requestResource)
			} else {
				// File recreated (atomic save)
				lib.onFileChange(filesCommon.IFileChange{
					Type:     filesCommon.FileChangeTypeUpdated,
					Resource: requestResource,
					CId:      lib.request.CorrelationId,
				}, true)

				// Restart watching
				disposable, err := lib.doWatch(false)
				if err == nil {
					lib.Register(disposable)
				}
			}
		}()
	} else if event.Op&fsnotify.Write != 0 {
		// File modified
		lib.onFileChange(filesCommon.IFileChange{
			Type:     filesCommon.FileChangeTypeUpdated,
			Resource: requestResource,
			CId:      lib.request.CorrelationId,
		}, true)
	}
}

// onWatchedPathDeleted handles when the watched path is deleted
func (lib *NodeJSFileWatcherLibrary) onWatchedPathDeleted(resource *baseCommon.URI) {
	lib.warn("Watcher shutdown because watched path got deleted")

	// Emit events and flush
	lib.onFileChange(filesCommon.IFileChange{
		Type:     filesCommon.FileChangeTypeDeleted,
		Resource: resource,
		CId:      lib.request.CorrelationId,
	}, true)
	lib.fileChangesAggregator.Flush()

	lib.notifyWatchFailed()
}

// onFileChange handles a file change event
func (lib *NodeJSFileWatcherLibrary) onFileChange(event filesCommon.IFileChange, skipIncludeExcludeChecks bool) {
	if lib.cts.Token().IsCancellationRequested() {
		return
	}

	// Logging
	if lib.verboseLogging {
		var eventType string
		switch event.Type {
		case filesCommon.FileChangeTypeAdded:
			eventType = "[ADDED]"
		case filesCommon.FileChangeTypeDeleted:
			eventType = "[DELETED]"
		default:
			eventType = "[CHANGED]"
		}
		lib.traceWithCorrelation(fmt.Sprintf("%s %s", eventType, event.Resource.GetFSPath()))
	}

	// Check excludes and includes
	if !skipIncludeExcludeChecks {
		// Check excludes
		for _, exclude := range lib.excludes {
			if lib.matchesPattern(exclude, event.Resource.GetFSPath()) {
				if lib.verboseLogging {
					lib.traceWithCorrelation(fmt.Sprintf(" >> ignored (excluded) %s", event.Resource.GetFSPath()))
				}
				return
			}
		}

		// Check includes
		if len(lib.includes) > 0 {
			matched := false
			for _, include := range lib.includes {
				if lib.matchesPattern(include, event.Resource.GetFSPath()) {
					matched = true
					break
				}
			}
			if !matched {
				if lib.verboseLogging {
					lib.traceWithCorrelation(fmt.Sprintf(" >> ignored (not included) %s", event.Resource.GetFSPath()))
				}
				return
			}
		}
	}

	lib.fileChangesAggregator.Work(event)
}

// matchesPattern checks if a path matches a pattern
func (lib *NodeJSFileWatcherLibrary) matchesPattern(pattern interface{}, path string) bool {
	// Simplified pattern matching - in a real implementation, this would use glob patterns
	if str, ok := pattern.(string); ok {
		return strings.Contains(path, str)
	}
	return false
}

// handleFileChanges handles aggregated file changes
func (lib *NodeJSFileWatcherLibrary) handleFileChanges(fileChanges []filesCommon.IFileChange) {
	// Coalesce events
	coalescedFileChanges := filesCommon.CoalesceEvents(fileChanges)

	// Filter events
	var filteredEvents []filesCommon.IFileChange
	for _, event := range coalescedFileChanges {
		if filesCommon.IsFiltered(event, lib.filter) {
			if lib.verboseLogging {
				lib.traceWithCorrelation(fmt.Sprintf(" >> ignored (filtered) %s", event.Resource.GetFSPath()))
			}
			continue
		}
		filteredEvents = append(filteredEvents, event)
	}

	if len(filteredEvents) == 0 {
		return
	}

	// Logging
	if lib.verboseLogging {
		for _, event := range filteredEvents {
			var eventType string
			switch event.Type {
			case filesCommon.FileChangeTypeAdded:
				eventType = "[ADDED]"
			case filesCommon.FileChangeTypeDeleted:
				eventType = "[DELETED]"
			default:
				eventType = "[CHANGED]"
			}
			lib.traceWithCorrelation(fmt.Sprintf(" >> normalized %s %s", eventType, event.Resource.GetFSPath()))
		}
	}

	// Emit via throttled emitter
	worked := lib.throttledFileChangesEmitter.Work(filteredEvents)

	if !worked {
		lib.warn(fmt.Sprintf("started ignoring events due to too many file change events at once (incoming: %d, most recent change: %s). Use 'files.watcherExclude' setting to exclude folders with lots of changing files (e.g. compilation output).", len(filteredEvents), filteredEvents[0].Resource.GetFSPath()))
	} else if lib.throttledFileChangesEmitter.Pending() > 0 {
		lib.trace(fmt.Sprintf("started throttling events due to large amount of file change events at once (pending: %d, most recent change: %s). Use 'files.watcherExclude' setting to exclude folders with lots of changing files (e.g. compilation output).", lib.throttledFileChangesEmitter.Pending(), filteredEvents[0].Resource.GetFSPath()))
	}
}

// SetVerboseLogging sets verbose logging
func (lib *NodeJSFileWatcherLibrary) SetVerboseLogging(verboseLogging bool) {
	lib.verboseLogging = verboseLogging
}

// error logs an error message
func (lib *NodeJSFileWatcherLibrary) error(message string) {
	if !lib.cts.Token().IsCancellationRequested() && lib.onLogMessage != nil {
		lib.onLogMessage(filesCommon.ILogMessage{
			Type:    "error",
			Message: fmt.Sprintf("[File Watcher (node.js)] %s", message),
		})
	}
}

// warn logs a warning message
func (lib *NodeJSFileWatcherLibrary) warn(message string) {
	if !lib.cts.Token().IsCancellationRequested() && lib.onLogMessage != nil {
		lib.onLogMessage(filesCommon.ILogMessage{
			Type:    "warn",
			Message: fmt.Sprintf("[File Watcher (node.js)] %s", message),
		})
	}
}

// trace logs a trace message
func (lib *NodeJSFileWatcherLibrary) trace(message string) {
	if !lib.cts.Token().IsCancellationRequested() && lib.verboseLogging && lib.onLogMessage != nil {
		lib.onLogMessage(filesCommon.ILogMessage{
			Type:    "trace",
			Message: fmt.Sprintf("[File Watcher (node.js)] %s", message),
		})
	}
}

// traceWithCorrelation logs a trace message with correlation
func (lib *NodeJSFileWatcherLibrary) traceWithCorrelation(message string) {
	if !lib.cts.Token().IsCancellationRequested() && lib.verboseLogging {
		correlationSuffix := ""
		if lib.request.CorrelationId != nil {
			correlationSuffix = fmt.Sprintf(" <%d> ", *lib.request.CorrelationId)
		}
		lib.trace(message + correlationSuffix)
	}
}

// Dispose disposes the watcher
func (lib *NodeJSFileWatcherLibrary) Dispose() {
	lib.cts.Cancel()
	lib.Disposable.Dispose()
}

// WatchFileContents watches file contents and returns data in chunks
func WatchFileContents(
	path string,
	onData func([]byte),
	onReady func(),
	token cancellation.CancellationToken,
	bufferSize int,
) error {
	if bufferSize <= 0 {
		bufferSize = 512
	}

	file, err := os.Open(path)
	if err != nil {
		return err
	}
	defer file.Close()

	buffer := make([]byte, bufferSize)

	cts := cancellation.NewCancellationTokenSourceWithToken(token)
	defer cts.Cancel()

	var isReading bool
	var readError error

	request := filesCommon.INonRecursiveWatchRequest{
		IWatchRequest: filesCommon.IWatchRequest{
			Path:      path,
			Recursive: false,
			Excludes:  []string{},
		},
	}

	watcher := NewNodeJSFileWatcherLibrary(
		request,
		nil,
		func(changes []filesCommon.IFileChange) {
			go func() {
				for _, change := range changes {
					if change.Type == filesCommon.FileChangeTypeUpdated {
						if isReading {
							return
						}

						isReading = true
						defer func() { isReading = false }()

						for !cts.Token().IsCancellationRequested() {
							n, err := file.Read(buffer)
							if n == 0 || err != nil {
								if err != nil {
									readError = err
									cts.Cancel()
								}
								break
							}

							onData(buffer[:n])
						}
					}
				}
			}()
		},
		nil,
		nil,
		false,
	)

	<-watcher.Ready()
	onReady()

	<-cts.Token().Done()
	watcher.Dispose()

	return readError
}
