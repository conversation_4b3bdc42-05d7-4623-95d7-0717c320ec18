/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package watcher

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	baseCommon "vscode-go/src/vs/base/common"
	"vscode-go/src/vs/base/common/async"
	"vscode-go/src/vs/base/common/hash"
	"vscode-go/src/vs/base/common/lifecycle"
	filesCommon "vscode-go/src/vs/platform/files/common"
)

// ISuspendedWatchRequest represents a suspended watch request
type ISuspendedWatchRequest struct {
	ID            int    `json:"id"`
	CorrelationId *int   `json:"correlationId"`
	Path          string `json:"path"`
}

// BaseWatcher is an abstract base class for file watchers
type BaseWatcher struct {
	lifecycle.Disposable

	onDidChangeFile baseCommon.Emitter[[]filesCommon.IFileChange]
	onDidLogMessage baseCommon.Emitter[filesCommon.ILogMessage]
	onDidWatchFail  baseCommon.Emitter[filesCommon.IUniversalWatchRequest]

	correlatedWatchRequests    map[int]filesCommon.IWatchRequestWithCorrelation
	nonCorrelatedWatchRequests map[int]filesCommon.IUniversalWatchRequest

	suspendedWatchRequests            map[int]*lifecycle.DisposableStore
	suspendedWatchRequestsWithPolling map[int]bool

	updateWatchersDelayer *async.ThrottledDelayer[struct{}]

	suspendedWatchRequestPollingInterval time.Duration

	joinWatch chan struct{}

	verboseLogging bool

	mutex sync.RWMutex
}

// NewBaseWatcher creates a new BaseWatcher instance
func NewBaseWatcher() *BaseWatcher {
	bw := &BaseWatcher{
		onDidChangeFile:                      baseCommon.NewEmitter[[]filesCommon.IFileChange](),
		onDidLogMessage:                      baseCommon.NewEmitter[filesCommon.ILogMessage](),
		onDidWatchFail:                       baseCommon.NewEmitter[filesCommon.IUniversalWatchRequest](),
		correlatedWatchRequests:              make(map[int]filesCommon.IWatchRequestWithCorrelation),
		nonCorrelatedWatchRequests:           make(map[int]filesCommon.IUniversalWatchRequest),
		suspendedWatchRequests:               make(map[int]*lifecycle.DisposableStore),
		suspendedWatchRequestsWithPolling:    make(map[int]bool),
		suspendedWatchRequestPollingInterval: 5007 * time.Millisecond, // node.js default
		joinWatch:                            make(chan struct{}, 1),
	}

	bw.updateWatchersDelayer = async.NewThrottledDelayer[struct{}](bw.getUpdateWatchersDelay(), func() (struct{}, error) {
		return struct{}{}, bw.updateWatchers(false)
	})

	// Register watch fail handler
	bw.onDidWatchFail.Event(func(request filesCommon.IUniversalWatchRequest) {
		bw.suspendWatchRequest(ISuspendedWatchRequest{
			ID:            bw.computeId(request),
			CorrelationId: bw.getCorrelationId(request),
			Path:          request.GetPath(),
		})
	})

	return bw
}

// OnDidChangeFile returns the file change event
func (bw *BaseWatcher) OnDidChangeFile() baseCommon.Event[[]filesCommon.IFileChange] {
	return bw.onDidChangeFile.Event
}

// OnDidLogMessage returns the log message event
func (bw *BaseWatcher) OnDidLogMessage() baseCommon.Event[filesCommon.ILogMessage] {
	return bw.onDidLogMessage.Event
}

// OnDidError returns the error event (abstract - must be implemented by subclasses)
func (bw *BaseWatcher) OnDidError() baseCommon.Event[filesCommon.IWatcherErrorEvent] {
	panic("OnDidError must be implemented by subclasses")
}

// isCorrelated checks if a request has correlation
func (bw *BaseWatcher) isCorrelated(request filesCommon.IUniversalWatchRequest) bool {
	return request.GetCorrelationId() != nil
}

// getCorrelationId gets the correlation ID from a request
func (bw *BaseWatcher) getCorrelationId(request filesCommon.IUniversalWatchRequest) *int {
	return request.GetCorrelationId()
}

// computeId computes an ID for a watch request
func (bw *BaseWatcher) computeId(request filesCommon.IUniversalWatchRequest) int {
	if bw.isCorrelated(request) {
		return *request.GetCorrelationId()
	}
	// Generate hash-based ID for non-correlated requests
	return hash.Hash(fmt.Sprintf("%s:%t:%v:%v:%v",
		request.GetPath(),
		request.GetRecursive(),
		request.GetExcludes(),
		request.GetIncludes(),
		request.GetFilter()))
}

// Watch configures the watcher according to the requests
func (bw *BaseWatcher) Watch(requests []filesCommon.IUniversalWatchRequest) error {
	bw.mutex.Lock()
	defer bw.mutex.Unlock()

	// Signal completion of previous watch
	select {
	case bw.joinWatch <- struct{}{}:
	default:
	}

	// Clear existing requests
	bw.correlatedWatchRequests = make(map[int]filesCommon.IWatchRequestWithCorrelation)
	bw.nonCorrelatedWatchRequests = make(map[int]filesCommon.IUniversalWatchRequest)

	// Categorize requests
	for _, request := range requests {
		id := bw.computeId(request)
		if bw.isCorrelated(request) {
			if corrReq, ok := request.(filesCommon.IWatchRequestWithCorrelation); ok {
				bw.correlatedWatchRequests[id] = corrReq
			}
		} else {
			bw.nonCorrelatedWatchRequests[id] = request
		}
	}

	// Remove suspended requests that are no longer watched
	for id := range bw.suspendedWatchRequests {
		if _, exists := bw.nonCorrelatedWatchRequests[id]; !exists {
			if _, exists := bw.correlatedWatchRequests[id]; !exists {
				if disposables, ok := bw.suspendedWatchRequests[id]; ok {
					disposables.Dispose()
					delete(bw.suspendedWatchRequests, id)
					delete(bw.suspendedWatchRequestsWithPolling, id)
				}
			}
		}
	}

	return bw.updateWatchers(false)
}

// updateWatchers updates the watchers
func (bw *BaseWatcher) updateWatchers(delayed bool) error {
	var nonSuspendedRequests []filesCommon.IUniversalWatchRequest

	// Collect non-suspended requests
	for id, request := range bw.nonCorrelatedWatchRequests {
		if _, suspended := bw.suspendedWatchRequests[id]; !suspended {
			nonSuspendedRequests = append(nonSuspendedRequests, request)
		}
	}
	for id, request := range bw.correlatedWatchRequests {
		if _, suspended := bw.suspendedWatchRequests[id]; !suspended {
			nonSuspendedRequests = append(nonSuspendedRequests, request)
		}
	}

	if delayed {
		_, err := bw.updateWatchersDelayer.Trigger()
		return err
	}

	return bw.doWatch(nonSuspendedRequests)
}

// getUpdateWatchersDelay returns the delay for updating watchers
func (bw *BaseWatcher) getUpdateWatchersDelay() time.Duration {
	return 800 * time.Millisecond
}

// IsSuspended checks if a request is suspended
func (bw *BaseWatcher) IsSuspended(request filesCommon.IUniversalWatchRequest) interface{} {
	bw.mutex.RLock()
	defer bw.mutex.RUnlock()

	id := bw.computeId(request)
	if bw.suspendedWatchRequestsWithPolling[id] {
		return "polling"
	}
	if _, suspended := bw.suspendedWatchRequests[id]; suspended {
		return true
	}
	return false
}

// suspendWatchRequest suspends a watch request
func (bw *BaseWatcher) suspendWatchRequest(request ISuspendedWatchRequest) {
	bw.mutex.Lock()
	defer bw.mutex.Unlock()

	if _, exists := bw.suspendedWatchRequests[request.ID]; exists {
		return // already suspended
	}

	disposables := lifecycle.NewDisposableStore()
	bw.suspendedWatchRequests[request.ID] = disposables

	// Wait for join watch to complete
	go func() {
		<-bw.joinWatch

		if !disposables.IsDisposed() {
			bw.monitorSuspendedWatchRequest(request, disposables)
			bw.updateWatchers(true)
		}
	}()
}

// resumeWatchRequest resumes a watch request
func (bw *BaseWatcher) resumeWatchRequest(request ISuspendedWatchRequest) {
	bw.mutex.Lock()
	defer bw.mutex.Unlock()

	if disposables, exists := bw.suspendedWatchRequests[request.ID]; exists {
		disposables.Dispose()
		delete(bw.suspendedWatchRequests, request.ID)
		delete(bw.suspendedWatchRequestsWithPolling, request.ID)

		bw.updateWatchers(false)
	}
}

// monitorSuspendedWatchRequest monitors a suspended watch request
func (bw *BaseWatcher) monitorSuspendedWatchRequest(request ISuspendedWatchRequest, disposables *lifecycle.DisposableStore) {
	if bw.doMonitorWithExistingWatcher(request, disposables) {
		bw.trace(fmt.Sprintf("reusing an existing recursive watcher to monitor %s", request.Path))
		delete(bw.suspendedWatchRequestsWithPolling, request.ID)
	} else {
		bw.doMonitorWithNodeJS(request, disposables)
		bw.suspendedWatchRequestsWithPolling[request.ID] = true
	}
}

// doMonitorWithExistingWatcher monitors with existing watcher (abstract)
func (bw *BaseWatcher) doMonitorWithExistingWatcher(request ISuspendedWatchRequest, disposables *lifecycle.DisposableStore) bool {
	// This should be implemented by subclasses that have recursive watchers
	return false
}

// doMonitorWithNodeJS monitors with Node.js file watching
func (bw *BaseWatcher) doMonitorWithNodeJS(request ISuspendedWatchRequest, disposables *lifecycle.DisposableStore) {
	bw.trace(fmt.Sprintf("starting file monitoring on %s (correlationId: %v)", request.Path, request.CorrelationId))

	// Use a simple file existence check with polling
	ctx, cancel := context.WithCancel(context.Background())
	disposables.Add(lifecycle.ToDisposable(cancel))

	go func() {
		ticker := time.NewTicker(bw.suspendedWatchRequestPollingInterval)
		defer ticker.Stop()

		pathNotFound := true

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				_, err := os.Stat(request.Path)
				currentPathNotFound := os.IsNotExist(err)

				// Path created: resume watching
				if !currentPathNotFound && pathNotFound {
					bw.onMonitoredPathAdded(request)
					return
				}

				pathNotFound = currentPathNotFound
			}
		}
	}()
}

// onMonitoredPathAdded handles when a monitored path is added
func (bw *BaseWatcher) onMonitoredPathAdded(request ISuspendedWatchRequest) {
	bw.trace(fmt.Sprintf("detected %s exists again, resuming watcher (correlationId: %v)", request.Path, request.CorrelationId))

	// Emit as event
	event := filesCommon.IFileChange{
		Type:     filesCommon.FileChangeTypeAdded,
		Resource: baseCommon.NewURI("file", "", request.Path, "", ""),
		CId:      request.CorrelationId,
	}
	bw.onDidChangeFile.Fire([]filesCommon.IFileChange{event})
	bw.traceEvent(event, request)

	// Resume watching
	bw.resumeWatchRequest(request)
}

// Stop stops all watchers
func (bw *BaseWatcher) Stop() error {
	bw.mutex.Lock()
	defer bw.mutex.Unlock()

	for _, disposables := range bw.suspendedWatchRequests {
		disposables.Dispose()
	}
	bw.suspendedWatchRequests = make(map[int]*lifecycle.DisposableStore)
	bw.suspendedWatchRequestsWithPolling = make(map[int]bool)

	return nil
}

// traceEvent traces a file change event
func (bw *BaseWatcher) traceEvent(event filesCommon.IFileChange, request interface{}) {
	if bw.verboseLogging {
		var eventType string
		switch event.Type {
		case filesCommon.FileChangeTypeAdded:
			eventType = "[ADDED]"
		case filesCommon.FileChangeTypeDeleted:
			eventType = "[DELETED]"
		default:
			eventType = "[CHANGED]"
		}

		traceMsg := fmt.Sprintf(" >> normalized %s %s", eventType, event.Resource.GetFSPath())
		bw.traceWithCorrelation(traceMsg, request)
	}
}

// traceWithCorrelation traces a message with correlation
func (bw *BaseWatcher) traceWithCorrelation(message string, request interface{}) {
	if bw.verboseLogging {
		var correlationId *int
		if req, ok := request.(ISuspendedWatchRequest); ok {
			correlationId = req.CorrelationId
		} else if req, ok := request.(filesCommon.IUniversalWatchRequest); ok {
			correlationId = req.GetCorrelationId()
		}

		if correlationId != nil {
			message = fmt.Sprintf("%s <%d> ", message, *correlationId)
		}
		bw.trace(message)
	}
}

// requestToString converts a request to string representation
func (bw *BaseWatcher) requestToString(request filesCommon.IUniversalWatchRequest) string {
	excludes := "<none>"
	if len(request.GetExcludes()) > 0 {
		excludes = fmt.Sprintf("%v", request.GetExcludes())
	}

	includes := "<all>"
	if len(request.GetIncludes()) > 0 {
		includes = fmt.Sprintf("%v", request.GetIncludes())
	}

	filter := filesCommon.RequestFilterToString(request.GetFilter())

	correlationId := "<none>"
	if request.GetCorrelationId() != nil {
		correlationId = fmt.Sprintf("%d", *request.GetCorrelationId())
	}

	return fmt.Sprintf("%s (excludes: %s, includes: %s, filter: %s, correlationId: %s)",
		request.GetPath(), excludes, includes, filter, correlationId)
}

// Abstract methods that must be implemented by subclasses
func (bw *BaseWatcher) doWatch(requests []filesCommon.IUniversalWatchRequest) error {
	panic("doWatch must be implemented by subclasses")
}

func (bw *BaseWatcher) trace(message string) {
	panic("trace must be implemented by subclasses")
}

func (bw *BaseWatcher) warn(message string) {
	panic("warn must be implemented by subclasses")
}

// SetVerboseLogging enables or disables verbose logging
func (bw *BaseWatcher) SetVerboseLogging(enabled bool) error {
	bw.verboseLogging = enabled
	return nil
}
