/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"strconv"
	"sync"
)

// IMessage represents a message with ID and data
type IMessage struct {
	ID   string `json:"id"`
	Data string `json:"data"`
}

// IVsdaSigner interface for VSDA signer
type IVsdaSigner interface {
	Sign(arg string) string
}

// IVsdaValidator interface for VSDA validator
type IVsdaValidator interface {
	CreateNewMessage(arg string) string
	Validate(arg string) string // returns "ok" or "error"
	Dispose() error
}

// AbstractSignService provides base implementation for sign services
type AbstractSignService struct {
	nextID     int
	validators map[string]IVsdaValidator
	mutex      sync.RWMutex
}

// NewAbstractSignService creates a new AbstractSignService
func NewAbstractSignService() *AbstractSignService {
	return &AbstractSignService{
		nextID:     1,
		validators: make(map[string]IVsdaValidator),
	}
}

// GetValidator should be implemented by concrete classes
func (a *AbstractSignService) GetValidator() (IVsdaValidator, error) {
	panic("GetValidator must be implemented by concrete classes")
}

// SignValue should be implemented by concrete classes
func (a *AbstractSignService) SignValue(arg string) (string, error) {
	panic("SignValue must be implemented by concrete classes")
}

// CreateNewMessage creates a new message with validation
func (a *AbstractSignService) CreateNewMessage(value string) (IMessage, error) {
	validator, err := a.GetValidator()
	if err != nil {
		// ignore errors silently and return original value
		return IMessage{ID: "", Data: value}, nil
	}

	if validator != nil {
		a.mutex.Lock()
		id := strconv.Itoa(a.nextID)
		a.nextID++
		a.validators[id] = validator
		a.mutex.Unlock()

		data := validator.CreateNewMessage(value)
		return IMessage{ID: id, Data: data}, nil
	}

	return IMessage{ID: "", Data: value}, nil
}

// Validate validates a message against a value
func (a *AbstractSignService) Validate(message IMessage, value string) (bool, error) {
	if message.ID == "" {
		return true, nil
	}

	a.mutex.Lock()
	validator, exists := a.validators[message.ID]
	if !exists {
		a.mutex.Unlock()
		return false, nil
	}
	delete(a.validators, message.ID)
	a.mutex.Unlock()

	defer func() {
		if validator != nil {
			validator.Dispose()
		}
	}()

	result := validator.Validate(value)
	return result == "ok", nil
}

// Sign signs a value
func (a *AbstractSignService) Sign(value string) (string, error) {
	signedValue, err := a.SignValue(value)
	if err != nil {
		// ignore errors silently and return original value
		return value, nil
	}
	return signedValue, nil
}
