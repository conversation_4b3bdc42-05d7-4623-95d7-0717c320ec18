/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

const SIGN_SERVICE_ID = "signService"

// ISignService interface for signing services
type ISignService interface {
	CreateNewMessage(value string) (IMessage, error)
	Validate(message IMessage, value string) (bool, error)
	Sign(value string) (string, error)
}
