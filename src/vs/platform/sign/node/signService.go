/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"errors"
	"fmt"
	"strconv"
	"sync"
)

// IMessage represents a message with ID and data
type IMessage struct {
	ID   string `json:"id"`
	Data string `json:"data"`
}

// ISignService interface for signing services
type ISignService interface {
	CreateNewMessage(value string) (IMessage, error)
	Validate(message IMessage, value string) (bool, error)
	Sign(value string) (string, error)
}

// IVsdaValidator interface for VSDA validator
type IVsdaValidator interface {
	CreateNewMessage(arg string) string
	Validate(arg string) string // returns "ok" or "error"
	Dispose() error
}

// VsdaSigner represents the native vsda signer
type VsdaSigner struct{}

// Sign signs the argument using VSDA
func (v *VsdaSigner) Sign(arg string) string {
	// In Go, we would need to use CGO or a native library binding
	// For now, this is a placeholder implementation
	// In a real implementation, this would call the native vsda library
	return fmt.Sprintf("signed_%s", arg)
}

// VsdaValidator represents the native vsda validator
type VsdaValidator struct{}

// CreateNewMessage creates a new message for validation
func (v *VsdaValidator) CreateNewMessage(arg string) string {
	// Placeholder implementation
	return fmt.Sprintf("message_%s", arg)
}

// Validate validates the argument
func (v *VsdaValidator) Validate(arg string) string {
	// Placeholder implementation - in real scenario this would validate using VSDA
	if arg != "" {
		return "ok"
	}
	return "error"
}

// Dispose cleans up the validator
func (v *VsdaValidator) Dispose() error {
	// Cleanup logic would go here
	return nil
}

// AbstractSignService provides base implementation for sign services
type AbstractSignService struct {
	nextID     int
	validators map[string]IVsdaValidator
	mutex      sync.RWMutex
}

// NewAbstractSignService creates a new AbstractSignService
func NewAbstractSignService() *AbstractSignService {
	return &AbstractSignService{
		nextID:     1,
		validators: make(map[string]IVsdaValidator),
	}
}

// CreateNewMessage creates a new message with validation
func (a *AbstractSignService) CreateNewMessage(value string) (IMessage, error) {
	validator, err := a.GetValidator()
	if err != nil {
		// ignore errors silently and return original value
		return IMessage{ID: "", Data: value}, nil
	}

	if validator != nil {
		a.mutex.Lock()
		id := strconv.Itoa(a.nextID)
		a.nextID++
		a.validators[id] = validator
		a.mutex.Unlock()

		data := validator.CreateNewMessage(value)
		return IMessage{ID: id, Data: data}, nil
	}

	return IMessage{ID: "", Data: value}, nil
}

// Validate validates a message against a value
func (a *AbstractSignService) Validate(message IMessage, value string) (bool, error) {
	if message.ID == "" {
		return true, nil
	}

	a.mutex.Lock()
	validator, exists := a.validators[message.ID]
	if !exists {
		a.mutex.Unlock()
		return false, nil
	}
	delete(a.validators, message.ID)
	a.mutex.Unlock()

	defer func() {
		if validator != nil {
			validator.Dispose()
		}
	}()

	result := validator.Validate(value)
	return result == "ok", nil
}

// Sign signs a value
func (a *AbstractSignService) Sign(value string) (string, error) {
	signedValue, err := a.SignValue(value)
	if err != nil {
		// ignore errors silently and return original value
		return value, nil
	}
	return signedValue, nil
}

// GetValidator should be implemented by concrete classes
func (a *AbstractSignService) GetValidator() (IVsdaValidator, error) {
	panic("GetValidator must be implemented by concrete classes")
}

// SignValue should be implemented by concrete classes
func (a *AbstractSignService) SignValue(arg string) (string, error) {
	panic("SignValue must be implemented by concrete classes")
}

// SignService implements ISignService using VSDA
type SignService struct {
	*AbstractSignService
}

// NewSignService creates a new SignService
func NewSignService() *SignService {
	return &SignService{
		AbstractSignService: NewAbstractSignService(),
	}
}

// GetValidator returns a VSDA validator
func (s *SignService) GetValidator() (IVsdaValidator, error) {
	// In the original TypeScript, this dynamically imports the vsda module
	// In Go, we would need to link against the native library
	// For now, return our Go implementation
	return &VsdaValidator{}, nil
}

// SignValue signs a value using VSDA
func (s *SignService) SignValue(arg string) (string, error) {
	// In the original TypeScript, this dynamically imports the vsda module
	// In Go, we would need to link against the native library
	// For now, return our Go implementation
	signer := &VsdaSigner{}
	return signer.Sign(arg), nil
}

// LoadVsda loads the VSDA module (placeholder for native library loading)
func (s *SignService) LoadVsda() error {
	// In a real implementation, this would load the native VSDA library
	// using CGO or a similar mechanism
	// For now, this is a placeholder
	return errors.New("VSDA native module not available in Go implementation")
}
