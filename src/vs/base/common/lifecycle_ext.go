/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package lifecycle

// MutableDisposable represents a mutable disposable
type MutableDisposable[T IDisposable] struct {
	value T
	disposed bool
}

// NewMutableDisposable creates a new mutable disposable
func NewMutableDisposable[T IDisposable]() *MutableDisposable[T] {
	return &MutableDisposable[T]{}
}

// Value gets the current value
func (md *MutableDisposable[T]) Value() T {
	return md.value
}

// SetValue sets the value
func (md *MutableDisposable[T]) SetValue(value T) {
	if md.disposed {
		if value != nil {
			value.Dispose()
		}
		return
	}
	
	if md.value != nil {
		md.value.Dispose()
	}
	md.value = value
}

// Clear clears the value
func (md *MutableDisposable[T]) Clear() {
	md.SetValue(*new(T))
}

// Dispose disposes the mutable disposable
func (md *MutableDisposable[T]) Dispose() {
	if md.disposed {
		return
	}
	
	md.disposed = true
	if md.value != nil {
		md.value.Dispose()
	}
}

// ToDisposable converts a function to a disposable
func ToDisposable(fn func()) IDisposable {
	return &funcDisposable{fn: fn}
}

type funcDisposable struct {
	fn func()
}

func (d *funcDisposable) Dispose() {
	if d.fn != nil {
		d.fn()
		d.fn = nil
	}
}
