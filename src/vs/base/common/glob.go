/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"reflect"
)

// IRelativePattern represents a relative pattern with base and pattern
type IRelativePattern struct {
	Base    string `json:"base"`
	Pattern string `json:"pattern"`
}

// PatternsEquals compares two arrays of patterns (strings or IRelativePattern) for equality
func PatternsEquals(patternsA []interface{}, patternsB []interface{}) bool {
	if len(patternsA) != len(patternsB) {
		return false
	}

	for i := 0; i < len(patternsA); i++ {
		a := patternsA[i]
		b := patternsB[i]

		// Both are strings
		if aStr, aOk := a.(string); aOk {
			if bStr, bOk := b.(string); bOk {
				if aStr != bStr {
					return false
				}
				continue
			}
			return false
		}

		// Both are IRelativePattern
		if aPattern, aOk := a.(IRelativePattern); aOk {
			if bPattern, bOk := b.(IRelativePattern); bOk {
				if aPattern.Base != bPattern.Base || aPattern.Pattern != bPattern.Pattern {
					return false
				}
				continue
			}
			return false
		}

		// Use reflection for other types
		if !reflect.DeepEqual(a, b) {
			return false
		}
	}

	return true
}
