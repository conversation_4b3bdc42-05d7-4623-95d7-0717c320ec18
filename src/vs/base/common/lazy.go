/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"sync"
)

// Lazy represents a lazily evaluated value
type Lazy[T any] struct {
	executor func() T
	didRun   bool
	value    T
	err      error
	mutex    sync.Mutex
}

// NewLazy creates a new Lazy instance with the given executor function
func NewLazy[T any](executor func() T) *Lazy[T] {
	return &Lazy[T]{
		executor: executor,
		didRun:   false,
	}
}

// HasValue returns true if the lazy value has been resolved
func (l *Lazy[T]) HasValue() bool {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	return l.didRun
}

// Value gets the wrapped value, forcing evaluation if not yet resolved
// This will re-throw exceptions that are hit while resolving the value
func (l *Lazy[T]) Value() (T, error) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	if !l.didRun {
		defer func() {
			l.didRun = true
		}()

		if r := recover(); r != nil {
			if err, ok := r.(error); ok {
				l.err = err
			} else {
				l.err = NewLazyError("panic during lazy evaluation")
			}
		} else {
			l.value = l.executor()
		}
	}

	if l.err != nil {
		var zero T
		return zero, l.err
	}
	return l.value, nil
}

// RawValue gets the wrapped value without forcing evaluation
func (l *Lazy[T]) RawValue() (T, bool) {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	return l.value, l.didRun
}

// LazyError represents an error that occurred during lazy evaluation
type LazyError struct {
	message string
}

func (e *LazyError) Error() string {
	return e.message
}

// NewLazyError creates a new lazy error with the given message
func NewLazyError(message string) error {
	return &LazyError{message: message}
}
