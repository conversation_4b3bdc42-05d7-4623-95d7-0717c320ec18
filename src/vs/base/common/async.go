/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"errors"
	"sync"
	"time"
)

// ITask represents a task that can be executed
type ITask[T any] func() T

// ITaskAsync represents an async task that can be executed
type ITaskAsync[T any] func() <-chan T

// Task is a convenience type for void tasks
type Task = ITask[interface{}]

// AsyncTask is a convenience type for async void tasks
type AsyncTask = ITaskAsync[interface{}]

// ILimiter interface for controlling concurrent execution
type ILimiter[T any] interface {
	Size() int
	Queue(factory ITaskAsync[T]) <-chan T
	Clear()
	Dispose()
}

// CancellationError represents a cancellation error
type CancellationError struct{}

func (e CancellationError) Error() string {
	return "operation was cancelled"
}

// ThrottledWorkerOptions represents options for throttled worker
type ThrottledWorkerOptions struct {
	MaxWorkChunkSize int
	ThrottleDelay    time.Duration
	MaxBufferedWork  int
}

// ThrottledWorker represents a throttled worker
type ThrottledWorker[T any] struct {
	options   ThrottledWorkerOptions
	processor func([]T)
	buffer    []T
	pending   int
	mutex     sync.Mutex
	timer     *time.Timer
}

// NewThrottledWorker creates a new throttled worker
func NewThrottledWorker[T any](options ThrottledWorkerOptions, processor func([]T)) *ThrottledWorker[T] {
	return &ThrottledWorker[T]{
		options:   options,
		processor: processor,
		buffer:    make([]T, 0),
	}
}

// Work adds work to the throttled worker
func (tw *ThrottledWorker[T]) Work(items []T) bool {
	tw.mutex.Lock()
	defer tw.mutex.Unlock()

	// Check if we can accept more work
	if len(tw.buffer)+len(items) > tw.options.MaxBufferedWork {
		return false
	}

	tw.buffer = append(tw.buffer, items...)
	tw.pending = len(tw.buffer)

	// Schedule processing
	if tw.timer != nil {
		tw.timer.Stop()
	}

	tw.timer = time.AfterFunc(tw.options.ThrottleDelay, func() {
		tw.processBuffer()
	})

	return true
}

// Pending returns the number of pending items
func (tw *ThrottledWorker[T]) Pending() int {
	tw.mutex.Lock()
	defer tw.mutex.Unlock()
	return tw.pending
}

// processBuffer processes the buffered items
func (tw *ThrottledWorker[T]) processBuffer() {
	tw.mutex.Lock()
	defer tw.mutex.Unlock()

	if len(tw.buffer) == 0 {
		return
	}

	// Process in chunks
	chunkSize := tw.options.MaxWorkChunkSize
	if chunkSize <= 0 || chunkSize > len(tw.buffer) {
		chunkSize = len(tw.buffer)
	}

	chunk := make([]T, chunkSize)
	copy(chunk, tw.buffer[:chunkSize])
	tw.buffer = tw.buffer[chunkSize:]
	tw.pending = len(tw.buffer)

	// Process the chunk
	go tw.processor(chunk)

	// Schedule next processing if there's more work
	if len(tw.buffer) > 0 {
		tw.timer = time.AfterFunc(tw.options.ThrottleDelay, func() {
			tw.processBuffer()
		})
	}
}

// RunOnceWorker represents a run-once worker
type RunOnceWorker[T any] struct {
	processor func([]T)
	delay     time.Duration
	buffer    []T
	timer     *time.Timer
	mutex     sync.Mutex
}

// NewRunOnceWorker creates a new run-once worker
func NewRunOnceWorker[T any](processor func([]T), delay time.Duration) *RunOnceWorker[T] {
	return &RunOnceWorker[T]{
		processor: processor,
		delay:     delay,
		buffer:    make([]T, 0),
	}
}

// Work adds work to the run-once worker
func (row *RunOnceWorker[T]) Work(item T) {
	row.mutex.Lock()
	defer row.mutex.Unlock()

	row.buffer = append(row.buffer, item)

	// Reset timer
	if row.timer != nil {
		row.timer.Stop()
	}

	row.timer = time.AfterFunc(row.delay, func() {
		row.flush()
	})
}

// Flush processes all buffered items immediately
func (row *RunOnceWorker[T]) Flush() {
	row.mutex.Lock()
	defer row.mutex.Unlock()
	row.flush()
}

// flush processes all buffered items (internal, assumes mutex is held)
func (row *RunOnceWorker[T]) flush() {
	if len(row.buffer) == 0 {
		return
	}

	items := make([]T, len(row.buffer))
	copy(items, row.buffer)
	row.buffer = row.buffer[:0] // Clear buffer

	if row.timer != nil {
		row.timer.Stop()
		row.timer = nil
	}

	go row.processor(items)
}

// ThrottledDelayer represents a throttled delayer
type ThrottledDelayer[T any] struct {
	delay     time.Duration
	processor func() (T, error)
	timer     *time.Timer
	mutex     sync.Mutex
}

// NewThrottledDelayer creates a new throttled delayer
func NewThrottledDelayer[T any](delay time.Duration, processor func() (T, error)) *ThrottledDelayer[T] {
	return &ThrottledDelayer[T]{
		delay:     delay,
		processor: processor,
	}
}

// Trigger triggers the delayer
func (td *ThrottledDelayer[T]) Trigger() (T, error) {
	td.mutex.Lock()
	defer td.mutex.Unlock()

	// Reset timer
	if td.timer != nil {
		td.timer.Stop()
	}

	// Execute immediately for now (simplified implementation)
	return td.processor()
}

// DeferredPromise represents a promise that can be resolved/rejected imperatively
type DeferredPromise[T any] struct {
	promise chan T
	errChan chan error
	value   T
	err     error
	settled bool
	mu      sync.RWMutex
}

// NewDeferredPromise creates a new deferred promise
func NewDeferredPromise[T any]() *DeferredPromise[T] {
	return &DeferredPromise[T]{
		promise: make(chan T, 1),
		errChan: make(chan error, 1),
	}
}

// Promise returns the promise channel
func (dp *DeferredPromise[T]) Promise() <-chan T {
	return dp.promise
}

// Error returns the error channel
func (dp *DeferredPromise[T]) Error() <-chan error {
	return dp.errChan
}

// Complete resolves the promise with a value
func (dp *DeferredPromise[T]) Complete(value T) {
	dp.mu.Lock()
	defer dp.mu.Unlock()

	if dp.settled {
		return
	}

	dp.value = value
	dp.settled = true
	dp.promise <- value
}

// Reject rejects the promise with an error
func (dp *DeferredPromise[T]) Reject(err error) {
	dp.mu.Lock()
	defer dp.mu.Unlock()

	if dp.settled {
		return
	}

	dp.err = err
	dp.settled = true
	dp.errChan <- err
}

// Cancel cancels the promise
func (dp *DeferredPromise[T]) Cancel() {
	dp.Reject(CancellationError{})
}

// IsSettled returns whether the promise is settled
func (dp *DeferredPromise[T]) IsSettled() bool {
	dp.mu.RLock()
	defer dp.mu.RUnlock()
	return dp.settled
}

// IsResolved returns whether the promise is resolved
func (dp *DeferredPromise[T]) IsResolved() bool {
	dp.mu.RLock()
	defer dp.mu.RUnlock()
	return dp.settled && dp.err == nil
}

// IsRejected returns whether the promise is rejected
func (dp *DeferredPromise[T]) IsRejected() bool {
	dp.mu.RLock()
	defer dp.mu.RUnlock()
	return dp.settled && dp.err != nil
}

// Value returns the resolved value (if resolved)
func (dp *DeferredPromise[T]) Value() T {
	dp.mu.RLock()
	defer dp.mu.RUnlock()
	return dp.value
}

// Err returns the rejection error (if rejected)
func (dp *DeferredPromise[T]) Err() error {
	dp.mu.RLock()
	defer dp.mu.RUnlock()
	return dp.err
}

// Limiter controls the degree of parallelism for promise execution
type Limiter[T any] struct {
	maxDegreeOfParallelism int
	runningPromises        int
	outstandingPromises    []limitedTaskFactory[T]
	onDrained              *Emitter[interface{}]
	size                   int
	isDisposed             bool
	mu                     sync.Mutex
}

type limitedTaskFactory[T any] struct {
	factory ITaskAsync[T]
	result  chan T
	err     chan error
}

// NewLimiter creates a new limiter with the specified degree of parallelism
func NewLimiter[T any](maxDegreeOfParallelism int) *Limiter[T] {
	return &Limiter[T]{
		maxDegreeOfParallelism: maxDegreeOfParallelism,
		outstandingPromises:    make([]limitedTaskFactory[T], 0),
		onDrained:              NewEmitter[interface{}](),
	}
}

// Size returns the number of queued tasks
func (l *Limiter[T]) Size() int {
	l.mu.Lock()
	defer l.mu.Unlock()
	return l.size
}

// Queue queues a task for execution
func (l *Limiter[T]) Queue(factory ITaskAsync[T]) <-chan T {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.isDisposed {
		result := make(chan T, 1)
		close(result)
		return result
	}

	result := make(chan T, 1)
	errChan := make(chan error, 1)

	task := limitedTaskFactory[T]{
		factory: factory,
		result:  result,
		err:     errChan,
	}

	l.outstandingPromises = append(l.outstandingPromises, task)
	l.size++

	go l.consume()

	return result
}

// consume processes queued tasks
func (l *Limiter[T]) consume() {
	l.mu.Lock()

	if l.runningPromises >= l.maxDegreeOfParallelism || len(l.outstandingPromises) == 0 {
		l.mu.Unlock()
		return
	}

	task := l.outstandingPromises[0]
	l.outstandingPromises = l.outstandingPromises[1:]
	l.runningPromises++

	l.mu.Unlock()

	// Execute task
	go func() {
		defer l.consumed()

		select {
		case result := <-task.factory():
			task.result <- result
		case <-time.After(time.Second * 30): // Timeout after 30 seconds
			task.err <- errors.New("task timeout")
		}
	}()
}

// consumed is called when a task completes
func (l *Limiter[T]) consumed() {
	l.mu.Lock()
	l.runningPromises--
	l.size--

	shouldEmitDrained := l.runningPromises == 0 && len(l.outstandingPromises) == 0

	l.mu.Unlock()

	if shouldEmitDrained {
		l.onDrained.Fire(nil)
	}

	go l.consume()
}

// Clear clears all queued tasks
func (l *Limiter[T]) Clear() {
	l.mu.Lock()
	defer l.mu.Unlock()

	for _, task := range l.outstandingPromises {
		close(task.result)
		close(task.err)
	}

	l.outstandingPromises = make([]limitedTaskFactory[T], 0)
	l.size = len(l.outstandingPromises)
}

// OnDrained returns an event that fires when all tasks are completed
func (l *Limiter[T]) OnDrained() Event[interface{}] {
	return l.onDrained
}

// WhenIdle waits until all tasks are completed
func (l *Limiter[T]) WhenIdle() <-chan interface{} {
	result := make(chan interface{}, 1)

	l.mu.Lock()
	if l.runningPromises == 0 && len(l.outstandingPromises) == 0 {
		l.mu.Unlock()
		result <- nil
		return result
	}
	l.mu.Unlock()

	// Subscribe to drained event
	disposable := l.onDrained.SubscribeOnce(func(interface{}) {
		result <- nil
	})

	go func() {
		<-result
		disposable.Dispose()
	}()

	return result
}

// Dispose disposes the limiter
func (l *Limiter[T]) Dispose() {
	l.mu.Lock()
	defer l.mu.Unlock()

	l.isDisposed = true
	l.Clear()
	l.onDrained.Dispose()
}

// Queue is a simple FIFO queue that ensures only one task runs at a time
type Queue[T any] struct {
	*Limiter[T]
}

// NewQueue creates a new queue
func NewQueue[T any]() *Queue[T] {
	return &Queue[T]{
		Limiter: NewLimiter[T](1),
	}
}

// ResourceQueue manages queues per resource URI
type ResourceQueue struct {
	queues             map[string]*Queue[interface{}]
	drainers           []*DeferredPromise[interface{}]
	drainListeners     *DisposableMap[int, IDisposable]
	drainListenerCount int
	mu                 sync.RWMutex
}

// NewResourceQueue creates a new resource queue
func NewResourceQueue() *ResourceQueue {
	return &ResourceQueue{
		queues:   make(map[string]*Queue[interface{}]),
		drainers: make([]*DeferredPromise[interface{}], 0),
	}
}

// WhenDrained waits until all queues are drained
func (rq *ResourceQueue) WhenDrained() <-chan interface{} {
	rq.mu.Lock()
	defer rq.mu.Unlock()

	if rq.isDrained() {
		result := make(chan interface{}, 1)
		result <- nil
		return result
	}

	promise := NewDeferredPromise[interface{}]()
	rq.drainers = append(rq.drainers, promise)

	return promise.Promise()
}

// isDrained checks if all queues are drained
func (rq *ResourceQueue) isDrained() bool {
	for _, queue := range rq.queues {
		if queue.Size() > 0 {
			return false
		}
	}
	return true
}

// QueueSize returns the size of the queue for a specific resource
func (rq *ResourceQueue) QueueSize(resource *URI) int {
	key := resource.ToString()

	rq.mu.RLock()
	defer rq.mu.RUnlock()

	if queue, exists := rq.queues[key]; exists {
		return queue.Size()
	}
	return 0
}

// QueueFor queues a task for a specific resource
func (rq *ResourceQueue) QueueFor(resource *URI, factory ITaskAsync[interface{}]) <-chan interface{} {
	key := resource.ToString()

	rq.mu.Lock()

	queue, exists := rq.queues[key]
	if !exists {
		queue = NewQueue[interface{}]()
		drainListenerID := rq.drainListenerCount
		rq.drainListenerCount++

		// Set up drain listener
		drainListener := queue.OnDrained().SubscribeOnce(func(interface{}) {
			rq.mu.Lock()
			delete(rq.queues, key)
			rq.onDidQueueDrain()

			if rq.drainListeners != nil {
				rq.drainListeners.Delete(drainListenerID)
				// Note: We'll clean up the drainListeners when the ResourceQueue is disposed
			}
			rq.mu.Unlock()
		})

		if rq.drainListeners == nil {
			rq.drainListeners = NewDisposableMap[int, IDisposable]()
		}
		rq.drainListeners.Set(drainListenerID, drainListener.(IDisposable))

		rq.queues[key] = queue
	}

	rq.mu.Unlock()

	return queue.Queue(factory)
}

// onDidQueueDrain is called when a queue is drained
func (rq *ResourceQueue) onDidQueueDrain() {
	if !rq.isDrained() {
		return
	}

	rq.releaseDrainers()
}

// releaseDrainers releases all waiting drainers
func (rq *ResourceQueue) releaseDrainers() {
	for _, drainer := range rq.drainers {
		drainer.Complete(nil)
	}
	rq.drainers = make([]*DeferredPromise[interface{}], 0)
}

// Dispose disposes the resource queue
func (rq *ResourceQueue) Dispose() {
	rq.mu.Lock()
	defer rq.mu.Unlock()

	for _, queue := range rq.queues {
		queue.Dispose()
	}

	rq.queues = make(map[string]*Queue[interface{}])
	rq.releaseDrainers()

	if rq.drainListeners != nil {
		rq.drainListeners.Dispose()
		rq.drainListeners = nil
	}
}

// Promises namespace for promise utilities
type Promises struct{}

// Settled waits for all promises to complete, similar to Promise.allSettled
func (p Promises) Settled(promises []<-chan interface{}) <-chan []interface{} {
	result := make(chan []interface{}, 1)

	if len(promises) == 0 {
		result <- []interface{}{}
		return result
	}

	go func() {
		results := make([]interface{}, len(promises))
		var wg sync.WaitGroup
		var firstError error
		var errorMu sync.Mutex

		for i, promise := range promises {
			wg.Add(1)
			go func(index int, p <-chan interface{}) {
				defer wg.Done()

				select {
				case value := <-p:
					results[index] = value
				case <-time.After(time.Second * 30): // Timeout
					errorMu.Lock()
					if firstError == nil {
						firstError = errors.New("promise timeout")
					}
					errorMu.Unlock()
				}
			}(i, promise)
		}

		wg.Wait()

		if firstError != nil {
			// For now, we'll still return results but could enhance error handling
		}

		result <- results
	}()

	return result
}

// SettledAsync is a convenience method for async tasks
func (p Promises) SettledAsync(tasks []ITaskAsync[interface{}]) <-chan []interface{} {
	promises := make([]<-chan interface{}, len(tasks))
	for i, task := range tasks {
		promises[i] = task()
	}
	return p.Settled(promises)
}

// WithAsyncBody creates a promise with an async body
func WithAsyncBody[T any](bodyFn func(resolve func(T), reject func(error))) <-chan T {
	result := make(chan T, 1)

	go func() {
		var resolved bool
		var mu sync.Mutex

		resolve := func(value T) {
			mu.Lock()
			if !resolved {
				resolved = true
				result <- value
			}
			mu.Unlock()
		}

		reject := func(err error) {
			mu.Lock()
			if !resolved {
				resolved = true
				close(result)
			}
			mu.Unlock()
		}

		bodyFn(resolve, reject)
	}()

	return result
}

// Global Promises instance
var PromisesInstance = Promises{}

// Timeout creates a promise that resolves after the specified duration
func Timeout(duration time.Duration) <-chan interface{} {
	result := make(chan interface{}, 1)
	go func() {
		time.Sleep(duration)
		result <- nil
	}()
	return result
}

// TimeoutWithCancel creates a cancellable timeout
func TimeoutWithCancel(duration time.Duration, cancel <-chan struct{}) <-chan interface{} {
	result := make(chan interface{}, 1)
	go func() {
		select {
		case <-time.After(duration):
			result <- nil
		case <-cancel:
			close(result)
		}
	}()
	return result
}

// Race returns the first promise to resolve
func Race[T any](promises []<-chan T) <-chan T {
	result := make(chan T, 1)

	if len(promises) == 0 {
		close(result)
		return result
	}

	for _, promise := range promises {
		go func(p <-chan T) {
			select {
			case value, ok := <-p:
				if ok {
					select {
					case result <- value:
					default:
					}
				}
			}
		}(promise)
	}

	return result
}

// All waits for all promises to resolve successfully
func All[T any](promises []<-chan T) <-chan []T {
	result := make(chan []T, 1)

	if len(promises) == 0 {
		result <- []T{}
		return result
	}

	go func() {
		results := make([]T, len(promises))
		var wg sync.WaitGroup

		for i, promise := range promises {
			wg.Add(1)
			go func(index int, p <-chan T) {
				defer wg.Done()
				if value, ok := <-p; ok {
					results[index] = value
				}
			}(i, promise)
		}

		wg.Wait()
		result <- results
	}()

	return result
}

// Sequencer executes tasks sequentially
type Sequencer struct {
	current chan interface{}
	mu      sync.Mutex
}

// NewSequencer creates a new sequencer
func NewSequencer() *Sequencer {
	initial := make(chan interface{}, 1)
	initial <- nil
	return &Sequencer{
		current: initial,
	}
}

// Queue queues a task to be executed sequentially
func (s *Sequencer) Queue(task ITaskAsync[interface{}]) <-chan interface{} {
	s.mu.Lock()
	defer s.mu.Unlock()

	result := make(chan interface{}, 1)

	go func() {
		// Wait for the current task to complete
		<-s.current

		// Execute the new task
		taskResult := task()
		value := <-taskResult

		result <- value

		// Update current
		s.mu.Lock()
		s.current = result
		s.mu.Unlock()
	}()

	return result
}
