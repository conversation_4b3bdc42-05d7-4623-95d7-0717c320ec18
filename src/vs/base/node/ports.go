package node

import (
	"net"
	"strconv"
	"time"
)

func FindFreePort(startPort int, giveUpAfter int, timeout int) (int, error) {
	done := make(chan int)

	go func() {
		port := doFindFreePort(startPort, giveUpAfter, 1)
		done <- port
	}()

	select {
	case port := <-done:
		return port, nil
	case <-time.After(time.Duration(timeout) * time.Millisecond):
		return 0, nil
	}
}

func doFindFreePort(startPort int, giveUpAfter int, stride int) int {
	if giveUpAfter == 0 {
		return 0
	}

	conn, err := net.Dial("tcp", net.JoinHostPort("127.0.0.1", strconv.Itoa(startPort)))
	if err != nil {
		return startPort
	}
	conn.Close()

	return doFindFreePort(startPort+stride, giveUpAfter-1, stride)
}

var BrowserRestrictedPorts = map[int]bool{
	1: true, 7: true, 9: true, 11: true, 13: true, 15: true, 17: true, 19: true,
	20: true, 21: true, 22: true, 23: true, 25: true, 37: true, 42: true,
	43: true, 53: true, 69: true, 77: true, 79: true, 87: true, 95: true,
	101: true, 102: true, 103: true, 104: true, 109: true, 110: true,
	111: true, 113: true, 115: true, 117: true, 119: true, 123: true,
	135: true, 137: true, 139: true, 143: true, 161: true, 179: true,
	389: true, 427: true, 465: true, 512: true, 513: true, 514: true,
	515: true, 526: true, 530: true, 531: true, 532: true, 540: true,
	548: true, 554: true, 556: true, 563: true, 587: true, 601: true,
	636: true, 989: true, 990: true, 993: true, 995: true, 1719: true,
	1720: true, 1723: true, 2049: true, 3659: true, 4045: true, 5060: true,
	5061: true, 6000: true, 6566: true, 6665: true, 6666: true, 6667: true,
	6668: true, 6669: true, 6697: true, 10080: true,
}

func IsPortFree(port int, timeout int) (bool, error) {
	portFound, err := FindFreePort(port, 0, timeout)
	return portFound != 0, err
}

func FindFreePortFaster(startPort int, giveUpAfter int, timeout int) (int, error) {
	// Implementation similar to FindFreePort
	return 0, nil // To be implemented
}
